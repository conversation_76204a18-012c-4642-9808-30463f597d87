<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء الصلاحيات
        $permissions = [
            // صلاحيات الطلبات
            ['name' => 'orders.create', 'display_name' => 'إنشاء طلب', 'description' => 'إنشاء طلبات جديدة', 'module' => 'orders'],
            ['name' => 'orders.view', 'display_name' => 'عرض الطلبات', 'description' => 'عرض قائمة الطلبات', 'module' => 'orders'],
            ['name' => 'orders.edit', 'display_name' => 'تعديل الطلبات', 'description' => 'تعديل الطلبات الموجودة', 'module' => 'orders'],
            ['name' => 'orders.delete', 'display_name' => 'حذف الطلبات', 'description' => 'حذف الطلبات', 'module' => 'orders'],
            ['name' => 'orders.status', 'display_name' => 'تغيير حالة الطلب', 'description' => 'تغيير حالة الطلبات', 'module' => 'orders'],

            // صلاحيات التقارير
            ['name' => 'reports.weekly', 'display_name' => 'التقارير الأسبوعية', 'description' => 'عرض وتحميل التقارير الأسبوعية', 'module' => 'reports'],
            ['name' => 'reports.monthly', 'display_name' => 'التقارير الشهرية', 'description' => 'عرض وتحميل التقارير الشهرية', 'module' => 'reports'],
            ['name' => 'reports.export', 'display_name' => 'تصدير التقارير', 'description' => 'تصدير التقارير بصيغ مختلفة', 'module' => 'reports'],

            // صلاحيات المستخدمين
            ['name' => 'users.view', 'display_name' => 'عرض المستخدمين', 'description' => 'عرض قائمة المستخدمين', 'module' => 'users'],
            ['name' => 'users.create', 'display_name' => 'إنشاء مستخدم', 'description' => 'إنشاء مستخدمين جدد', 'module' => 'users'],
            ['name' => 'users.edit', 'display_name' => 'تعديل المستخدمين', 'description' => 'تعديل بيانات المستخدمين', 'module' => 'users'],
            ['name' => 'users.delete', 'display_name' => 'حذف المستخدمين', 'description' => 'حذف المستخدمين', 'module' => 'users'],
            ['name' => 'users.permissions', 'display_name' => 'إدارة الصلاحيات', 'description' => 'إدارة صلاحيات المستخدمين', 'module' => 'users'],

            // صلاحيات النظام
            ['name' => 'system.settings', 'display_name' => 'إعدادات النظام', 'description' => 'الوصول لإعدادات النظام', 'module' => 'system'],
            ['name' => 'system.backup', 'display_name' => 'النسخ الاحتياطي', 'description' => 'إنشاء واستعادة النسخ الاحتياطية', 'module' => 'system'],
            ['name' => 'system.logs', 'display_name' => 'سجلات النظام', 'description' => 'عرض سجلات النظام', 'module' => 'system'],

            // صلاحيات الفروع
            ['name' => 'branches.view', 'display_name' => 'عرض الفروع', 'description' => 'عرض قائمة الفروع', 'module' => 'branches'],
            ['name' => 'branches.manage', 'display_name' => 'إدارة الفروع', 'description' => 'إنشاء وتعديل الفروع', 'module' => 'branches'],

            // صلاحيات عروض الأسعار
            ['name' => 'quotations.create', 'display_name' => 'إنشاء عرض سعر', 'description' => 'إنشاء عروض أسعار جديدة', 'module' => 'quotations'],
            ['name' => 'quotations.view', 'display_name' => 'عرض عروض الأسعار', 'description' => 'عرض قائمة عروض الأسعار', 'module' => 'quotations'],
            ['name' => 'quotations.edit', 'display_name' => 'تعديل عروض الأسعار', 'description' => 'تعديل عروض الأسعار الموجودة', 'module' => 'quotations'],
            ['name' => 'quotations.delete', 'display_name' => 'حذف عروض الأسعار', 'description' => 'حذف عروض الأسعار', 'module' => 'quotations'],
            ['name' => 'quotations.status', 'display_name' => 'تغيير حالة عرض السعر', 'description' => 'تغيير حالة عروض الأسعار', 'module' => 'quotations'],
            ['name' => 'quotations.convert', 'display_name' => 'تحويل عرض السعر لطلب', 'description' => 'تحويل عروض الأسعار إلى طلبات', 'module' => 'quotations'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name']],
                $permission
            );
        }

        // إنشاء الأدوار
        $roles = [
            [
                'name' => 'admin',
                'display_name' => 'مدير النظام',
                'description' => 'مدير النظام له صلاحية كاملة على جميع الوحدات',
                'permissions' => Permission::all()->pluck('name')->toArray()
            ],
            [
                'name' => 'manager',
                'display_name' => 'مدير القسم',
                'description' => 'مدير القسم له صلاحيات محدودة',
                'permissions' => [
                    'orders.create', 'orders.view', 'orders.edit', 'orders.status',
                    'quotations.create', 'quotations.view', 'quotations.edit', 'quotations.status', 'quotations.convert',
                    'reports.weekly', 'reports.monthly', 'reports.export',
                    'users.view', 'branches.view'
                ]
            ],
            [
                'name' => 'employee',
                'display_name' => 'موظف',
                'description' => 'موظف عادي له صلاحيات أساسية',
                'permissions' => [
                    'orders.create', 'orders.view', 'orders.edit',
                    'quotations.create', 'quotations.view', 'quotations.edit'
                ]
            ],
            [
                'name' => 'viewer',
                'display_name' => 'مشاهد',
                'description' => 'مشاهد فقط بدون صلاحيات تعديل',
                'permissions' => [
                    'orders.view', 'quotations.view', 'reports.weekly', 'reports.monthly'
                ]
            ]
        ];

        foreach ($roles as $roleData) {
            $role = Role::firstOrCreate(
                ['name' => $roleData['name']],
                [
                    'display_name' => $roleData['display_name'],
                    'description' => $roleData['description'],
                    'is_active' => true
                ]
            );

            // ربط الصلاحيات بالدور
            $permissions = Permission::whereIn('name', $roleData['permissions'])->get();
            $role->permissions()->sync($permissions->pluck('id')->toArray());
        }

        // إنشاء مستخدم مدير افتراضي
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'مدير النظام',
                'password' => Hash::make('admin123'),
                'user_type' => 'admin',
                'is_active' => true,
                'department' => 'الإدارة العامة',
                'position' => 'مدير النظام'
            ]
        );

        // ربط المستخدم المدير بدور المدير
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminUser->roles()->syncWithoutDetaching([$adminRole->id]);
        }

        // إنشاء مستخدم مدير قسم
        $managerUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'مدير القسم',
                'password' => Hash::make('manager123'),
                'user_type' => 'manager',
                'is_active' => true,
                'department' => 'قسم الطلبات',
                'position' => 'مدير القسم'
            ]
        );

        // ربط مدير القسم بدوره
        $managerRole = Role::where('name', 'manager')->first();
        if ($managerRole) {
            $managerUser->roles()->syncWithoutDetaching([$managerRole->id]);
        }

        // إنشاء موظف عادي
        $employeeUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'موظف الطلبات',
                'password' => Hash::make('employee123'),
                'user_type' => 'employee',
                'is_active' => true,
                'department' => 'قسم الطلبات',
                'position' => 'موظف'
            ]
        );

        // ربط الموظف بدوره
        $employeeRole = Role::where('name', 'employee')->first();
        if ($employeeRole) {
            $employeeUser->roles()->syncWithoutDetaching([$employeeRole->id]);
        }

        $this->command->info('تم إنشاء الأدوار والصلاحيات والمستخدمين بنجاح!');
        $this->command->info('المستخدمون المنشؤون:');
        $this->command->info('مدير النظام: <EMAIL> / admin123');
        $this->command->info('مدير القسم: <EMAIL> / manager123');
        $this->command->info('الموظف: <EMAIL> / employee123');
    }
}
