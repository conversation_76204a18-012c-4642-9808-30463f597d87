@extends('layouts.admin')

@section('title', 'قائمة عروض الأسعار')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-file-invoice-dollar text-primary me-2"></i>
                قائمة عروض الأسعار
            </h2>
            <p class="text-muted mb-0">إدارة وعرض جميع عروض الأسعار</p>
        </div>
        @if(hasPermission('create_quotations'))
        <a href="{{ route('quotations.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إنشاء عرض سعر جديد
        </a>
        @endif
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي العروض</h6>
                            <h4 class="mb-0">{{ $statistics['total_quotations'] }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-invoice-dollar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">مسودات</h6>
                            <h4 class="mb-0">{{ $statistics['draft_count'] }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-edit fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">مرسلة</h6>
                            <h4 class="mb-0">{{ $statistics['sent_count'] }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-paper-plane fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">مقبولة</h6>
                            <h4 class="mb-0">{{ $statistics['accepted_count'] }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">مرفوضة</h6>
                            <h4 class="mb-0">{{ $statistics['rejected_count'] }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">منتهية الصلاحية</h6>
                            <h4 class="mb-0">{{ $statistics['expired_count'] }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('quotations.index') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="رقم العرض، اسم العميل، رقم الهاتف...">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>مسودة</option>
                        <option value="sent" {{ request('status') == 'sent' ? 'selected' : '' }}>مرسل</option>
                        <option value="accepted" {{ request('status') == 'accepted' ? 'selected' : '' }}>مقبول</option>
                        <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                        <option value="expired" {{ request('status') == 'expired' ? 'selected' : '' }}>منتهي الصلاحية</option>
                        <option value="converted" {{ request('status') == 'converted' ? 'selected' : '' }}>تم تحويله لطلب</option>
                    </select>
                </div>
                @if(isAdmin())
                <div class="col-md-3">
                    <label for="branch_id" class="form-label">الفرع</label>
                    <select class="form-select" id="branch_id" name="branch_id">
                        <option value="">جميع الفروع</option>
                        @foreach($branches as $branch)
                            <option value="{{ $branch->id }}" {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                                {{ $branch->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                @endif
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                        <a href="{{ route('quotations.index') }}" class="btn btn-secondary">
                            <i class="fas fa-undo me-1"></i>
                            إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Quotations Table -->
    <div class="card">
        <div class="card-body">
            @if($quotations->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>رقم العرض</th>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>البضائع</th>
                                <th>قيمة الخدمة</th>
                                <th>صالح حتى</th>
                                <th>الحالة</th>
                                <th>الفرع</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($quotations as $quotation)
                                <tr>
                                    <td>
                                        <strong class="text-primary">{{ $quotation->formatted_quotation_number }}</strong>
                                    </td>
                                    <td>{{ $quotation->customer_name }}</td>
                                    <td>{{ $quotation->customer_phone ?: 'غير محدد' }}</td>
                                    <td>{{ $quotation->goods_name }}</td>
                                    <td>
                                        <span class="fw-bold text-success">
                                            {{ number_format($quotation->service_fees, 2) }} {{ $quotation->currency }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($quotation->valid_until)
                                            <span class="{{ $quotation->isExpired() ? 'text-danger' : 'text-success' }}">
                                                {{ $quotation->valid_until->format('Y-m-d') }}
                                            </span>
                                        @else
                                            <span class="text-muted">غير محدد</span>
                                        @endif
                                    </td>
                                    <td>
                                        @php
                                            $statusClasses = [
                                                'draft' => 'bg-secondary',
                                                'sent' => 'bg-info',
                                                'accepted' => 'bg-success',
                                                'rejected' => 'bg-danger',
                                                'expired' => 'bg-warning',
                                                'converted' => 'bg-primary'
                                            ];
                                        @endphp
                                        <span class="badge {{ $statusClasses[$quotation->status] ?? 'bg-secondary' }}">
                                            {{ $quotation->status_in_arabic }}
                                        </span>
                                    </td>
                                    <td>{{ $quotation->branch->name ?? 'غير محدد' }}</td>
                                    <td>{{ $quotation->created_at->format('Y-m-d H:i') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('quotations.show', $quotation) }}" 
                                               class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if(hasPermission('edit_quotations') && !in_array($quotation->status, ['accepted', 'converted']))
                                                <a href="{{ route('quotations.edit', $quotation) }}" 
                                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endif
                                            @if(hasPermission('delete_quotations') && !in_array($quotation->status, ['accepted', 'converted']))
                                                <form method="POST" action="{{ route('quotations.destroy', $quotation) }}" 
                                                      style="display: inline;" 
                                                      onsubmit="return confirm('هل أنت متأكد من حذف هذا العرض؟')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $quotations->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-file-invoice-dollar fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد عروض أسعار</h5>
                    <p class="text-muted">لم يتم العثور على أي عروض أسعار تطابق معايير البحث</p>
                    @if(hasPermission('create_quotations'))
                        <a href="{{ route('quotations.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء عرض سعر جديد
                        </a>
                    @endif
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Auto-submit form on filter change
    document.addEventListener('DOMContentLoaded', function() {
        const statusSelect = document.getElementById('status');
        const branchSelect = document.getElementById('branch_id');
        
        if (statusSelect) {
            statusSelect.addEventListener('change', function() {
                this.form.submit();
            });
        }
        
        if (branchSelect) {
            branchSelect.addEventListener('change', function() {
                this.form.submit();
            });
        }
    });
</script>
@endpush
