<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quotations', function (Blueprint $table) {
            $table->id();
            $table->string('quotation_number')->unique();
            $table->string('customer_name');
            $table->string('customer_phone')->nullable();
            $table->string('customer_email')->nullable();
            $table->text('customer_address')->nullable();
            $table->string('service_type')->nullable();
            $table->string('goods_name');
            $table->string('goods_type')->nullable();
            $table->string('country_of_origin')->nullable();
            $table->decimal('weight', 10, 2)->nullable();
            $table->string('quantity')->nullable();
            $table->unsignedBigInteger('branch_id');
            $table->string('departure_area')->nullable();
            $table->string('delivery_area')->nullable();
            $table->decimal('service_fees', 10, 2)->default(0);
            $table->string('currency')->default('ريال');
            $table->string('delivery_time')->nullable();
            $table->date('valid_until')->nullable();
            $table->text('notes')->nullable();
            $table->enum('status', ['draft', 'sent', 'accepted', 'rejected', 'expired', 'converted'])->default('draft');
            $table->unsignedBigInteger('user_id');
            $table->string('user_name')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('branch_id')->references('id')->on('branches')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['status', 'created_at']);
            $table->index(['branch_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index('valid_until');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quotations');
    }
};
