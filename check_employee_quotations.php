<?php
/**
 * فحص سريع لصلاحيات عروض الأسعار للموظفين
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'alahmadi_a';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>فحص صلاحيات عروض الأسعار للموظفين</h1>";
    
    // 1. التحقق من وجود صلاحيات عروض الأسعار
    echo "<h2>1. صلاحيات عروض الأسعار في قاعدة البيانات:</h2>";
    $quotationPermissions = $pdo->query("
        SELECT name, display_name FROM permissions 
        WHERE module = 'quotations' 
        ORDER BY name
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($quotationPermissions)) {
        echo "<p style='color: red;'>❌ لا توجد صلاحيات عروض الأسعار</p>";
        echo "<p><strong>الحل:</strong> تشغيل <a href='add_quotations_permissions.php' target='_blank'>add_quotations_permissions.php</a></p>";
    } else {
        echo "<p style='color: green;'>✅ موجود (" . count($quotationPermissions) . " صلاحيات):</p>";
        echo "<ul>";
        foreach ($quotationPermissions as $perm) {
            echo "<li><strong>{$perm['name']}</strong> - {$perm['display_name']}</li>";
        }
        echo "</ul>";
    }
    
    // 2. التحقق من دور الموظف
    echo "<h2>2. دور الموظف (employee):</h2>";
    $employeeRole = $pdo->query("SELECT * FROM roles WHERE name = 'employee'")->fetch(PDO::FETCH_ASSOC);
    
    if (!$employeeRole) {
        echo "<p style='color: red;'>❌ دور الموظف غير موجود</p>";
    } else {
        echo "<p style='color: green;'>✅ دور الموظف موجود: {$employeeRole['display_name']}</p>";
        
        // التحقق من صلاحيات دور الموظف لعروض الأسعار
        $employeeQuotationPermissions = $pdo->query("
            SELECT permission_name FROM role_permissions 
            WHERE role_id = {$employeeRole['id']} 
            AND permission_name LIKE '%quotation%'
        ")->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($employeeQuotationPermissions)) {
            echo "<p style='color: orange;'>⚠️ لا توجد صلاحيات عروض أسعار مربوطة بدور الموظف</p>";
        } else {
            echo "<p style='color: green;'>✅ صلاحيات عروض الأسعار لدور الموظف:</p>";
            echo "<ul>";
            foreach ($employeeQuotationPermissions as $perm) {
                echo "<li>{$perm}</li>";
            }
            echo "</ul>";
        }
    }
    
    // 3. التحقق من المستخدمين الموظفين
    echo "<h2>3. المستخدمين الموظفين:</h2>";
    $employees = $pdo->query("
        SELECT id, name, email, user_type, role 
        FROM users 
        WHERE user_type = 'employee' OR role = 'employee'
        LIMIT 5
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($employees)) {
        echo "<p style='color: orange;'>⚠️ لا يوجد موظفين في النظام</p>";
        echo "<p>يمكنك إنشاء موظف من صفحة <a href='public/permissions' target='_blank'>إدارة الصلاحيات</a></p>";
    } else {
        echo "<p style='color: green;'>✅ الموظفين الموجودين:</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>الاسم</th><th>البريد الإلكتروني</th><th>نوع المستخدم</th><th>مربوط بدور؟</th></tr>";
        
        foreach ($employees as $emp) {
            // التحقق من ربط المستخدم بدور
            $userRole = $pdo->query("
                SELECT r.display_name 
                FROM user_roles ur 
                JOIN roles r ON ur.role_id = r.id 
                WHERE ur.user_id = {$emp['id']}
            ")->fetch(PDO::FETCH_COLUMN);
            
            $roleStatus = $userRole ? "✅ {$userRole}" : "❌ غير مربوط";
            
            echo "<tr>";
            echo "<td>{$emp['name']}</td>";
            echo "<td>{$emp['email']}</td>";
            echo "<td>{$emp['user_type']}</td>";
            echo "<td>{$roleStatus}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 4. اختبار سريع للصلاحيات الافتراضية
    echo "<h2>4. الصلاحيات الافتراضية للموظفين:</h2>";
    echo "<p>حسب الكود، الموظفين لديهم الصلاحيات التالية افتراضياً:</p>";
    $defaultEmployeePermissions = [
        'create_orders' => 'إنشاء طلبات',
        'view_orders' => 'عرض الطلبات', 
        'view_own_orders' => 'عرض الطلبات الخاصة',
        'create_quotations' => '✅ إنشاء عروض أسعار',
        'view_quotations' => '✅ عرض عروض الأسعار',
        'edit_quotations' => '✅ تعديل عروض الأسعار'
    ];
    
    echo "<ul>";
    foreach ($defaultEmployeePermissions as $perm => $desc) {
        $color = strpos($perm, 'quotation') !== false ? 'green' : 'blue';
        echo "<li style='color: {$color};'><strong>{$perm}</strong> - {$desc}</li>";
    }
    echo "</ul>";
    
    // 5. روابط الاختبار
    echo "<h2>5. اختبار النظام:</h2>";
    echo "<div style='background-color: #f0f8ff; padding: 15px; border: 1px solid #0066cc; border-radius: 5px;'>";
    echo "<p><strong>للاختبار:</strong></p>";
    echo "<ol>";
    echo "<li><a href='public/login' target='_blank'>تسجيل الدخول</a> كموظف</li>";
    echo "<li>التحقق من ظهور قسم 'عروض الأسعار' في القائمة الجانبية</li>";
    echo "<li>محاولة الوصول إلى <a href='public/quotations' target='_blank'>قائمة عروض الأسعار</a></li>";
    echo "<li>محاولة <a href='public/quotations/create' target='_blank'>إنشاء عرض سعر جديد</a></li>";
    echo "</ol>";
    
    if (!empty($employees)) {
        echo "<p><strong>حسابات الاختبار:</strong></p>";
        echo "<ul>";
        foreach (array_slice($employees, 0, 3) as $emp) {
            echo "<li>{$emp['email']} - <a href='public/test-login/{$emp['email']}' target='_blank'>تسجيل دخول سريع</a></li>";
        }
        echo "</ul>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>
