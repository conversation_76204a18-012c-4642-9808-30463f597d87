<?php
/**
 * اختبار صلاحيات عروض الأسعار للموظفين
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'alahmadi_a';
$username = 'root';
$password = '';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>اختبار صلاحيات عروض الأسعار للموظفين</h1>";
    
    // 1. التحقق من وجود صلاحيات عروض الأسعار في جدول permissions
    echo "<h2>1. التحقق من صلاحيات عروض الأسعار</h2>";
    $quotationPermissions = $pdo->query("
        SELECT name, display_name, description, module 
        FROM permissions 
        WHERE module = 'quotations' 
        ORDER BY name
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($quotationPermissions)) {
        echo "<p style='color: red;'>❌ لا توجد صلاحيات عروض الأسعار في قاعدة البيانات</p>";
        echo "<p><strong>الحل:</strong> تشغيل ملف add_quotations_permissions.php</p>";
    } else {
        echo "<p style='color: green;'>✅ تم العثور على " . count($quotationPermissions) . " صلاحيات لعروض الأسعار:</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>اسم الصلاحية</th><th>الاسم المعروض</th><th>الوصف</th></tr>";
        foreach ($quotationPermissions as $perm) {
            echo "<tr>";
            echo "<td>{$perm['name']}</td>";
            echo "<td>{$perm['display_name']}</td>";
            echo "<td>{$perm['description']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 2. التحقق من الأدوار الموجودة
    echo "<h2>2. التحقق من الأدوار</h2>";
    $roles = $pdo->query("SELECT id, name, display_name FROM roles ORDER BY name")->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($roles)) {
        echo "<p style='color: red;'>❌ لا توجد أدوار في قاعدة البيانات</p>";
    } else {
        echo "<p style='color: green;'>✅ الأدوار الموجودة:</p>";
        echo "<ul>";
        foreach ($roles as $role) {
            echo "<li><strong>{$role['display_name']}</strong> ({$role['name']})</li>";
        }
        echo "</ul>";
    }
    
    // 3. التحقق من صلاحيات الأدوار لعروض الأسعار
    echo "<h2>3. صلاحيات الأدوار لعروض الأسعار</h2>";
    $rolePermissions = $pdo->query("
        SELECT r.name as role_name, r.display_name as role_display_name, 
               rp.permission_name, p.display_name as permission_display_name
        FROM roles r
        LEFT JOIN role_permissions rp ON r.id = rp.role_id
        LEFT JOIN permissions p ON rp.permission_name = p.name
        WHERE rp.permission_name LIKE '%quotations%' OR rp.permission_name LIKE '%quotation%'
        ORDER BY r.name, rp.permission_name
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($rolePermissions)) {
        echo "<p style='color: red;'>❌ لا توجد صلاحيات عروض الأسعار مربوطة بالأدوار</p>";
        echo "<p><strong>الحل:</strong> تشغيل ملف add_quotations_permissions.php</p>";
    } else {
        echo "<p style='color: green;'>✅ صلاحيات عروض الأسعار للأدوار:</p>";
        $currentRole = '';
        foreach ($rolePermissions as $rp) {
            if ($currentRole !== $rp['role_name']) {
                if ($currentRole !== '') echo "</ul>";
                echo "<h4>{$rp['role_display_name']} ({$rp['role_name']}):</h4>";
                echo "<ul>";
                $currentRole = $rp['role_name'];
            }
            echo "<li>{$rp['permission_display_name']} ({$rp['permission_name']})</li>";
        }
        if ($currentRole !== '') echo "</ul>";
    }
    
    // 4. التحقق من المستخدمين الموظفين
    echo "<h2>4. المستخدمين الموظفين</h2>";
    $employees = $pdo->query("
        SELECT id, name, email, user_type, role 
        FROM users 
        WHERE user_type = 'employee' OR role = 'employee'
        ORDER BY name
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($employees)) {
        echo "<p style='color: orange;'>⚠️ لا يوجد موظفين في النظام</p>";
    } else {
        echo "<p style='color: green;'>✅ الموظفين الموجودين:</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>الاسم</th><th>البريد الإلكتروني</th><th>نوع المستخدم</th><th>الدور</th></tr>";
        foreach ($employees as $emp) {
            echo "<tr>";
            echo "<td>{$emp['name']}</td>";
            echo "<td>{$emp['email']}</td>";
            echo "<td>{$emp['user_type']}</td>";
            echo "<td>{$emp['role']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 5. التحقق من صلاحيات المستخدمين المباشرة
    echo "<h2>5. الصلاحيات المباشرة للمستخدمين</h2>";
    $userPermissions = $pdo->query("
        SELECT u.name as user_name, u.email, up.permission_name, p.display_name as permission_display_name
        FROM users u
        JOIN user_permissions up ON u.id = up.user_id
        LEFT JOIN permissions p ON up.permission_name = p.name
        WHERE up.permission_name LIKE '%quotations%' OR up.permission_name LIKE '%quotation%'
        ORDER BY u.name, up.permission_name
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($userPermissions)) {
        echo "<p style='color: orange;'>⚠️ لا توجد صلاحيات مباشرة لعروض الأسعار للمستخدمين</p>";
        echo "<p><em>هذا طبيعي إذا كانت الصلاحيات تُدار من خلال الأدوار</em></p>";
    } else {
        echo "<p style='color: green;'>✅ الصلاحيات المباشرة:</p>";
        $currentUser = '';
        foreach ($userPermissions as $up) {
            if ($currentUser !== $up['user_name']) {
                if ($currentUser !== '') echo "</ul>";
                echo "<h4>{$up['user_name']} ({$up['email']}):</h4>";
                echo "<ul>";
                $currentUser = $up['user_name'];
            }
            echo "<li>{$up['permission_display_name']} ({$up['permission_name']})</li>";
        }
        if ($currentUser !== '') echo "</ul>";
    }
    
    // 6. التحقق من ربط المستخدمين بالأدوار
    echo "<h2>6. ربط المستخدمين بالأدوار</h2>";
    $userRoles = $pdo->query("
        SELECT u.name as user_name, u.email, u.user_type, r.name as role_name, r.display_name as role_display_name
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        LEFT JOIN roles r ON ur.role_id = r.id
        WHERE u.user_type = 'employee' OR u.role = 'employee'
        ORDER BY u.name
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($userRoles)) {
        echo "<p style='color: orange;'>⚠️ لا يوجد موظفين مربوطين بأدوار</p>";
    } else {
        echo "<p style='color: green;'>✅ ربط الموظفين بالأدوار:</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>اسم المستخدم</th><th>البريد الإلكتروني</th><th>نوع المستخدم</th><th>الدور المربوط</th></tr>";
        foreach ($userRoles as $ur) {
            $roleDisplay = $ur['role_display_name'] ? "{$ur['role_display_name']} ({$ur['role_name']})" : "غير مربوط بدور";
            echo "<tr>";
            echo "<td>{$ur['user_name']}</td>";
            echo "<td>{$ur['email']}</td>";
            echo "<td>{$ur['user_type']}</td>";
            echo "<td>{$roleDisplay}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 7. اختبار الصلاحيات الافتراضية للموظفين
    echo "<h2>7. الصلاحيات الافتراضية للموظفين</h2>";
    $defaultEmployeePermissions = [
        'create_orders', 'view_orders', 'view_own_orders',
        'create_quotations', 'view_quotations', 'edit_quotations'
    ];
    
    echo "<p>الصلاحيات الافتراضية للموظفين حسب الكود:</p>";
    echo "<ul>";
    foreach ($defaultEmployeePermissions as $perm) {
        $color = (strpos($perm, 'quotation') !== false) ? 'green' : 'blue';
        echo "<li style='color: {$color};'>{$perm}</li>";
    }
    echo "</ul>";
    
    // 8. التوصيات
    echo "<h2>8. التوصيات</h2>";
    echo "<div style='background-color: #f0f8ff; padding: 15px; border: 1px solid #0066cc; border-radius: 5px;'>";
    
    if (empty($quotationPermissions)) {
        echo "<p style='color: red;'><strong>مطلوب:</strong> تشغيل ملف add_quotations_permissions.php لإضافة صلاحيات عروض الأسعار</p>";
    }
    
    if (!empty($employees) && empty($userRoles)) {
        echo "<p style='color: orange;'><strong>تحسين:</strong> ربط الموظفين بدور 'employee' في جدول user_roles</p>";
    }
    
    echo "<p style='color: green;'><strong>للتأكد من عمل النظام:</strong></p>";
    echo "<ol>";
    echo "<li>تسجيل الدخول كموظف</li>";
    echo "<li>التحقق من ظهور قسم 'عروض الأسعار' في القائمة الجانبية</li>";
    echo "<li>محاولة إنشاء عرض سعر جديد</li>";
    echo "<li>محاولة عرض قائمة عروض الأسعار</li>";
    echo "</ol>";
    
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>
