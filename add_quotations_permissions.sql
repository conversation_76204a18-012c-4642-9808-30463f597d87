-- إضافة صلاحيات عروض الأسعار إلى قاعدة البيانات

-- إدراج صلاحيات عروض الأسعار في جدول permissions
INSERT INTO `permissions` (`name`, `display_name`, `description`, `module`, `created_at`, `updated_at`) VALUES
('create_quotations', 'إنشاء عرض سعر', 'إنشاء عروض أسعار جديدة', 'quotations', NOW(), NOW()),
('view_quotations', 'عرض عروض الأسعار', 'عرض قائمة عروض الأسعار', 'quotations', NOW(), NOW()),
('edit_quotations', 'تعديل عروض الأسعار', 'تعديل عروض الأسعار الموجودة', 'quotations', NOW(), NOW()),
('delete_quotations', 'حذف عروض الأسعار', 'حذف عروض الأسعار', 'quotations', NOW(), NOW()),
('quotations_status', 'تغيير حالة عرض السعر', 'تغيير حالة عروض الأسعار', 'quotations', NOW(), NOW()),
('convert_quotations', 'تحويل عرض السعر لطلب', 'تحويل عروض الأسعار إلى طلبات', 'quotations', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    `display_name` = VALUES(`display_name`),
    `description` = VALUES(`description`),
    `updated_at` = NOW();

-- إضافة صلاحيات عروض الأسعار لدور المدير (admin)
INSERT INTO `role_permissions` (`role_id`, `permission_name`, `granted_at`, `granted_by`, `created_at`, `updated_at`)
SELECT 
    r.id as role_id,
    p.name as permission_name,
    NOW() as granted_at,
    1 as granted_by,
    NOW() as created_at,
    NOW() as updated_at
FROM roles r
CROSS JOIN (
    SELECT 'create_quotations' as name
    UNION SELECT 'view_quotations'
    UNION SELECT 'edit_quotations'
    UNION SELECT 'delete_quotations'
    UNION SELECT 'quotations_status'
    UNION SELECT 'convert_quotations'
) p
WHERE r.name = 'admin'
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- إضافة صلاحيات عروض الأسعار لدور مدير القسم (manager)
INSERT INTO `role_permissions` (`role_id`, `permission_name`, `granted_at`, `granted_by`, `created_at`, `updated_at`)
SELECT 
    r.id as role_id,
    p.name as permission_name,
    NOW() as granted_at,
    1 as granted_by,
    NOW() as created_at,
    NOW() as updated_at
FROM roles r
CROSS JOIN (
    SELECT 'create_quotations' as name
    UNION SELECT 'view_quotations'
    UNION SELECT 'edit_quotations'
    UNION SELECT 'quotations_status'
    UNION SELECT 'convert_quotations'
) p
WHERE r.name = 'manager'
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- إضافة صلاحيات عروض الأسعار لدور الموظف (employee)
INSERT INTO `role_permissions` (`role_id`, `permission_name`, `granted_at`, `granted_by`, `created_at`, `updated_at`)
SELECT 
    r.id as role_id,
    p.name as permission_name,
    NOW() as granted_at,
    1 as granted_by,
    NOW() as created_at,
    NOW() as updated_at
FROM roles r
CROSS JOIN (
    SELECT 'create_quotations' as name
    UNION SELECT 'view_quotations'
    UNION SELECT 'edit_quotations'
) p
WHERE r.name = 'employee'
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- إضافة صلاحية عرض عروض الأسعار لدور المشاهد (viewer)
INSERT INTO `role_permissions` (`role_id`, `permission_name`, `granted_at`, `granted_by`, `created_at`, `updated_at`)
SELECT 
    r.id as role_id,
    'view_quotations' as permission_name,
    NOW() as granted_at,
    1 as granted_by,
    NOW() as created_at,
    NOW() as updated_at
FROM roles r
WHERE r.name = 'viewer'
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- التحقق من النتائج
SELECT 'تم إضافة الصلاحيات التالية:' as message;
SELECT p.name, p.display_name, p.module FROM permissions p WHERE p.module = 'quotations';

SELECT 'تم ربط الصلاحيات بالأدوار التالية:' as message;
SELECT r.display_name as role_name, rp.permission_name 
FROM role_permissions rp 
JOIN roles r ON r.id = rp.role_id 
WHERE rp.permission_name LIKE '%quotations%' OR rp.permission_name LIKE '%quotation%'
ORDER BY r.display_name, rp.permission_name;
