<?php $__env->startSection('title', 'إنشاء طلب جديد'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus-circle me-2"></i>
                    إنشاء طلب جديد
                </h5>
                <a href="<?php echo e(route('orders.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة إلى قائمة الطلبات
                </a>
            </div>
            <div class="card-body">
                <?php if($errors->any()): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>يرجى تصحيح الأخطاء التالية:</strong>
                        <ul class="mb-0 mt-2">
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <form action="<?php echo e(route('orders.store')); ?>" method="POST" id="orderForm">
                    <?php echo csrf_field(); ?>

                    <!-- معلومات الطلب الأساسية -->
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات الطلب الأساسية
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="branch_id" class="form-label">
                                            <i class="fas fa-code-branch me-1"></i>
                                            الفرع <span class="text-danger">*</span>
                                        </label>
                                        <?php if(isset($defaultBranchId) && $defaultBranchId && auth()->user()->user_type !== 'admin'): ?>
                                            <?php
                                                $userBranch = $branches->find($defaultBranchId);
                                            ?>
                                            <input type="hidden" name="branch_id" value="<?php echo e($defaultBranchId); ?>">
                                            <input type="text" class="form-control bg-light" 
                                                   value="<?php echo e($userBranch ? $userBranch->name . ' (' . $userBranch->code . ')' : 'غير محدد'); ?>" 
                                                   readonly>
                                            <small class="text-info">
                                                <i class="fas fa-info-circle me-1"></i>
                                                تم تحديد الفرع تلقائياً حسب حسابك
                                            </small>
                                        <?php else: ?>
                                            <select class="form-select" id="branch_id" name="branch_id" required>
                                                <option value="">اختر الفرع</option>
                                                <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($branch->id); ?>"
                                                            data-code="<?php echo e($branch->code); ?>"
                                                            <?php echo e((old('branch_id', $defaultBranchId) == $branch->id) ? 'selected' : ''); ?>>
                                                        <?php echo e($branch->name); ?> (<?php echo e($branch->code); ?>)
                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="order_number_preview" class="form-label">
                                            <i class="fas fa-hashtag me-1"></i>
                                            رقم الطلب
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-primary text-white">
                                                <i class="fas fa-barcode"></i>
                                            </span>
                                            <input type="text"
                                                   class="form-control bg-light fw-bold text-primary"
                                                   id="order_number_preview"
                                                   readonly
                                                   placeholder="اختر الفرع أولاً">
                                        </div>
                                        <small class="text-success">
                                            <i class="fas fa-magic me-1"></i>
                                            سيتم إنشاؤه تلقائياً
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="next_serial_info" class="form-label">
                                            <i class="fas fa-info-circle me-1"></i>
                                            معلومات الترقيم
                                        </label>
                                        <div class="card bg-light border-0">
                                            <div class="card-body p-2">
                                                <div id="serial_info" class="text-center">
                                                    <small class="text-muted">اختر الفرع لعرض المعلومات</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="customer_name" class="form-label">
                                            اسم العميل <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control"
                                               id="customer_name"
                                               name="customer_name"
                                               value="<?php echo e(old('customer_name')); ?>"
                                               placeholder="أدخل اسم العميل"
                                               required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="phone_number" class="form-label">
                                            رقم الهاتف <span class="text-danger">*</span>
                                        </label>
                                        <input type="tel"
                                               class="form-control"
                                               id="phone_number"
                                               name="phone_number"
                                               value="<?php echo e(old('phone_number')); ?>"
                                               placeholder="777123456"
                                               required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="recipient_name" class="form-label">اسم المستلم</label>
                                        <input type="text"
                                               class="form-control"
                                               id="recipient_name"
                                               name="recipient_name"
                                               value="<?php echo e(old('recipient_name')); ?>"
                                               placeholder="أدخل اسم المستلم">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="user_id" class="form-label">
                                            المستخدم المسؤول <span class="text-danger">*</span>
                                        </label>
                                        <?php if($users->count() == 1): ?>
                                            
                                            <input type="hidden" name="user_id" value="<?php echo e($users->first()->id); ?>">
                                            <input type="text" class="form-control" value="<?php echo e($users->first()->name); ?>" readonly>
                                            <div class="form-text text-muted">
                                                <small><i class="fas fa-info-circle me-1"></i>أنت مسؤول عن هذا الطلب</small>
                                            </div>
                                        <?php else: ?>
                                            
                                            <select class="form-select" id="user_id" name="user_id" required>
                                                <option value="">اختر المستخدم المسؤول</option>
                                                <?php $__currentLoopData = $users->where('is_active', true); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($user->id); ?>" <?php echo e(old('user_id', auth()->id()) == $user->id ? 'selected' : ''); ?>>
                                                        <?php echo e($user->name); ?>

                                                        <?php if($user->department): ?>
                                                            - <?php echo e($user->department); ?>

                                                        <?php endif; ?>
                                                        <?php if($user->position): ?>
                                                            (<?php echo e($user->position); ?>)
                                                        <?php endif; ?>
                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <div class="form-text text-muted">
                                                <small><i class="fas fa-users me-1"></i>اختر المستخدم المسؤول عن متابعة هذا الطلب</small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تفاصيل البضاعة والخدمة -->
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-boxes me-2"></i>
                                تفاصيل البضاعة والخدمة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="service_type" class="form-label">
                                            نوع الخدمة <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control"
                                               id="service_type"
                                               name="service_type"
                                               value="<?php echo e(old('service_type')); ?>"
                                               placeholder="أدخل نوع الخدمة (مثال: شحن بحري، شحن جوي، شحن بري)"
                                               required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="goods_name" class="form-label">
                                            اسم البضاعة <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control"
                                               id="goods_name"
                                               name="goods_name"
                                               value="<?php echo e(old('goods_name')); ?>"
                                               placeholder="أدخل اسم البضاعة"
                                               required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="goods_type" class="form-label">نوع البضاعة</label>
                                        <input type="text"
                                               class="form-control"
                                               id="goods_type"
                                               name="goods_type"
                                               value="<?php echo e(old('goods_type')); ?>"
                                               placeholder="أدخل نوع البضاعة (مثال: إلكترونيات، ملابس، أغذية، أدوية، قطع غيار، إلخ)">
                                        <div class="form-text text-muted">
                                            <small>يمكنك كتابة أي نوع بضاعة تريده</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="country_of_origin" class="form-label">بلد المنشأ</label>
                                        <input type="text"
                                               class="form-control"
                                               id="country_of_origin"
                                               name="country_of_origin"
                                               value="<?php echo e(old('country_of_origin')); ?>"
                                               placeholder="أدخل بلد المنشأ (مثال: الصين، تركيا، الإمارات، السعودية، الهند، إلخ)">
                                        <div class="form-text text-muted">
                                            <small>يمكنك كتابة أي بلد تريده</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="quantity" class="form-label">
                                            <i class="fas fa-boxes me-1"></i>
                                            الكمية
                                        </label>
                                        <input type="text"
                                               class="form-control"
                                               id="quantity"
                                               name="quantity"
                                               value="<?php echo e(old('quantity')); ?>"
                                               placeholder="مثال: 10 قطع، 5 كراتين، 2 طن">
                                        <div class="form-text text-muted">
                                            <small>أدخل الكمية مع الوحدة (قطع، كراتين، أطنان، إلخ)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="weight" class="form-label">
                                            <i class="fas fa-weight me-1"></i>
                                            الوزن (كيلو) <span class="text-danger">*</span>
                                        </label>
                                        <input type="number"
                                               class="form-control"
                                               id="weight"
                                               name="weight"
                                               value="<?php echo e(old('weight')); ?>"
                                               placeholder="25.5"
                                               required
                                               min="0.1"
                                               step="0.1">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الشحن -->
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header bg-warning text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-shipping-fast me-2"></i>
                                معلومات الشحن
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="departure_area" class="form-label">
                                            منطقة الانطلاق <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control"
                                               id="departure_area"
                                               name="departure_area"
                                               value="<?php echo e(old('departure_area')); ?>"
                                               placeholder="مثال: شنغهاي"
                                               required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="second_interface" class="form-label">الواجهة الثانية</label>
                                        <input type="text"
                                               class="form-control"
                                               id="second_interface"
                                               name="second_interface"
                                               value="<?php echo e(old('second_interface')); ?>"
                                               placeholder="أدخل الواجهة الثانية">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="delivery_area" class="form-label">
                                            منطقة التسليم <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control"
                                               id="delivery_area"
                                               name="delivery_area"
                                               value="<?php echo e(old('delivery_area')); ?>"
                                               placeholder="مثال: المكلا"
                                               required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="delivery_time" class="form-label">مدة الوصول</label>
                                        <input type="text"
                                               class="form-control"
                                               id="delivery_time"
                                               name="delivery_time"
                                               value="<?php echo e(old('delivery_time')); ?>"
                                               placeholder="15-20 يوم">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="request_date" class="form-label">تاريخ رفع الطلب</label>
                                        <input type="date"
                                               class="form-control"
                                               id="request_date"
                                               name="request_date"
                                               value="<?php echo e(old('request_date', date('Y-m-d'))); ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="scheduled_delivery_date" class="form-label">التاريخ المقرر للتسليم</label>
                                        <input type="date"
                                               class="form-control"
                                               id="scheduled_delivery_date"
                                               name="scheduled_delivery_date"
                                               value="<?php echo e(old('scheduled_delivery_date')); ?>">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- المعلومات المالية -->
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المعلومات المالية
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="service_fees" class="form-label">
                                            رسوم الخدمة <span class="text-danger">*</span>
                                        </label>
                                        <input type="number"
                                               class="form-control"
                                               id="service_fees"
                                               name="service_fees"
                                               value="<?php echo e(old('service_fees')); ?>"
                                               placeholder="1500"
                                               required
                                               min="0"
                                               step="0.01">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="currency" class="form-label">العملة</label>
                                        <select class="form-select" id="currency" name="currency">
                                            <option value="دولار" <?php echo e(old('currency', 'دولار') == 'دولار' ? 'selected' : ''); ?>>دولار</option>
                                            <option value="ريال" <?php echo e(old('currency') == 'ريال' ? 'selected' : ''); ?>>ريال</option>
                                            <option value="ريال سعودي" <?php echo e(old('currency') == 'ريال سعودي' ? 'selected' : ''); ?>>ريال سعودي</option>
                                            <option value="درهم" <?php echo e(old('currency') == 'درهم' ? 'selected' : ''); ?>>درهم</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="paid_amount" class="form-label">المدفوع</label>
                                        <input type="number"
                                               class="form-control"
                                               id="paid_amount"
                                               name="paid_amount"
                                               value="<?php echo e(old('paid_amount', '0')); ?>"
                                               placeholder="1000"
                                               min="0"
                                               step="0.01">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="remaining_amount" class="form-label">المتبقي</label>
                                        <input type="number"
                                               class="form-control bg-light"
                                               id="remaining_amount"
                                               name="remaining_amount"
                                               readonly
                                               step="0.01">
                                        <small class="text-muted">يتم حسابه تلقائياً</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="customer_agreed_amount" class="form-label">المبلغ المتفق مع العميل</label>
                                        <input type="number"
                                               class="form-control"
                                               id="customer_agreed_amount"
                                               name="customer_agreed_amount"
                                               value="<?php echo e(old('customer_agreed_amount')); ?>"
                                               placeholder="2000"
                                               min="0"
                                               step="0.01">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="agent_agreed_amount" class="form-label">المبلغ المتفق مع الوكيل</label>
                                        <input type="number"
                                               class="form-control"
                                               id="agent_agreed_amount"
                                               name="agent_agreed_amount"
                                               value="<?php echo e(old('agent_agreed_amount')); ?>"
                                               placeholder="1200"
                                               min="0"
                                               step="0.01">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="other_expenses" class="form-label">مصاريف أخرى</label>
                                        <input type="number"
                                               class="form-control"
                                               id="other_expenses"
                                               name="other_expenses"
                                               value="<?php echo e(old('other_expenses', '0')); ?>"
                                               placeholder="100"
                                               min="0"
                                               step="0.01">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="profit" class="form-label">الربح</label>
                                        <input type="number"
                                               class="form-control bg-light"
                                               id="profit"
                                               name="profit"
                                               readonly
                                               step="0.01">
                                        <small class="text-muted">يتم حسابه تلقائياً</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="status" class="form-label">حالة الطلب</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="قيد المتابعة" <?php echo e(old('status', 'قيد المتابعة') == 'قيد المتابعة' ? 'selected' : ''); ?>>قيد المتابعة</option>
                                            <option value="تم الاتفاق" <?php echo e(old('status') == 'تم الاتفاق' ? 'selected' : ''); ?>>تم الاتفاق</option>
                                            <option value="ملغي" <?php echo e(old('status') == 'ملغي' ? 'selected' : ''); ?>>ملغي</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات -->
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header bg-secondary text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-sticky-note me-2"></i>
                                ملاحظات
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="form-group mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control"
                                          id="notes"
                                          name="notes"
                                          rows="4"
                                          placeholder="أدخل ملاحظات إضافية (اختياري)"><?php echo e(old('notes')); ?></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="d-flex justify-content-between mt-4">
                        <a href="<?php echo e(route('orders.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-success px-4">
                            <i class="fas fa-save me-1"></i>
                            حفظ الطلب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<style>
    /* تنسيق الألوان مع القائمة الجانبية */
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.5rem;
    }

    .card-header {
        border-radius: 0.5rem 0.5rem 0 0 !important;
        border: none;
        font-weight: 600;
        padding: 1rem 1.5rem;
    }

    /* ألوان متدرجة تتناسق مع الشريط الجانبي */
    .bg-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }

    .bg-info {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }

    .bg-warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    }

    .bg-success {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    }

    .bg-secondary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }

    /* تنسيق الحقول */
    .form-control {
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        transition: all 0.15s ease-in-out;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: 0;
    }

    .form-select {
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        transition: all 0.15s ease-in-out;
    }

    .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: 0;
    }

    /* تنسيق الأزرار */
    .btn-success {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border: none;
        border-radius: 0.35rem;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.15s ease-in-out;
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
        transform: translateY(-1px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        border: none;
        border-radius: 0.35rem;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.15s ease-in-out;
    }

    .btn-secondary:hover {
        background: linear-gradient(135deg, #5a6268 0%, #3d4142 100%);
        transform: translateY(-1px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    /* تنسيق التسميات */
    .form-label {
        font-weight: 600;
        color: #5a5c69;
        margin-bottom: 0.5rem;
    }

    .text-danger {
        color: #e74a3b !important;
    }

    /* تنسيق الحقول المقفلة */
    .bg-light {
        background-color: #f8f9fc !important;
        border-color: #e3e6f0 !important;
    }

    /* تنسيق النصوص المساعدة */
    .text-muted {
        color: #858796 !important;
        font-size: 0.8rem;
    }

    /* تأثيرات التفاعل */
    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
        transition: all 0.3s ease-in-out;
    }

    /* تنسيق الأيقونات */
    .fas {
        margin-left: 0.5rem;
    }

    /* تأثيرات إضافية للحقول */
    .form-control:hover, .form-select:hover {
        border-color: #667eea;
        transition: all 0.15s ease-in-out;
    }

    /* تنسيق الحاوية الرئيسية */
    .container-fluid {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    /* تنسيق العنوان الرئيسي */
    .card-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #5a5c69;
    }

    /* تأثيرات الظلال المتدرجة */
    .shadow-sm {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075),
                    0 0.25rem 0.5rem rgba(102, 126, 234, 0.1) !important;
    }

    /* تنسيق النجمة الحمراء */
    .text-danger {
        font-weight: bold;
        font-size: 1.1em;
    }

    /* تحسين مظهر textarea */
    textarea.form-control {
        resize: vertical;
        min-height: 120px;
    }

    /* تنسيق الأزرار مع تأثيرات متقدمة */
    .btn {
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
        z-index: -1;
    }

    .btn:hover::before {
        left: 100%;
    }

    /* تنسيق responsive */
    @media (max-width: 768px) {
        .card-header {
            padding: 0.75rem 1rem;
        }

        .btn-success, .btn-secondary {
            padding: 0.5rem 1.5rem;
            font-size: 0.875rem;
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث رقم الطلب عند اختيار الفرع
    const branchSelect = document.getElementById('branch_id');
    const orderNumberPreview = document.getElementById('order_number_preview');
    const serialInfo = document.getElementById('serial_info');

    function updateOrderNumber() {
        let branchCode = null;
        let branchName = '';

        if (branchSelect) {
            // إذا كان هناك حقل اختيار للفرع
            const selectedOption = branchSelect.options[branchSelect.selectedIndex];
            branchCode = selectedOption.getAttribute('data-code');
            branchName = selectedOption.text;
        } else {
            // إذا كان الفرع محدد تلقائياً (مخفي)
            const hiddenBranchInput = document.querySelector('input[name="branch_id"][type="hidden"]');
            if (hiddenBranchInput) {
                const branchId = hiddenBranchInput.value;
                // البحث عن كود الفرع من البيانات المتاحة
                <?php if(isset($defaultBranchId) && $defaultBranchId): ?>
                    <?php
                        $userBranch = $branches->find($defaultBranchId);
                    ?>
                    <?php if($userBranch): ?>
                        branchCode = '<?php echo e($userBranch->code); ?>';
                        branchName = '<?php echo e($userBranch->name); ?>';
                    <?php endif; ?>
                <?php endif; ?>
            }
        }

        if (branchCode) {
            // إظهار loading
            if (orderNumberPreview) {
                orderNumberPreview.value = 'جاري التحميل...';
            }
            if (serialInfo) {
                serialInfo.innerHTML = '<small class="text-info"><i class="fas fa-spinner fa-spin me-1"></i>جاري التحميل...</small>';
            }

            // طلب AJAX للحصول على الرقم التسلسلي التالي
            fetch(`/api/orders/next-serial/${branchCode}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (orderNumberPreview) {
                            orderNumberPreview.value = data.order_number;
                        }

                        if (serialInfo) {
                            serialInfo.innerHTML = `
                                <div class="text-center">
                                    <div class="fw-bold text-primary">${data.order_number}</div>
                                    <small class="text-muted">الرقم التسلسلي: ${String(data.next_serial).padStart(2, '0')}</small><br>
                                    <small class="text-muted">السنة: 20${data.year}</small>
                                </div>
                            `;
                        }
                    } else {
                        if (orderNumberPreview) {
                            orderNumberPreview.value = 'خطأ في التحميل';
                        }
                        if (serialInfo) {
                            serialInfo.innerHTML = '<small class="text-danger">خطأ في تحميل البيانات</small>';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    if (orderNumberPreview) {
                        orderNumberPreview.value = 'خطأ في الاتصال';
                    }
                    if (serialInfo) {
                        serialInfo.innerHTML = '<small class="text-danger">خطأ في الاتصال بالخادم</small>';
                    }
                });
        } else {
            if (orderNumberPreview) {
                orderNumberPreview.value = '';
                orderNumberPreview.placeholder = 'اختر الفرع أولاً';
            }
            if (serialInfo) {
                serialInfo.innerHTML = '<small class="text-muted">اختر الفرع لعرض المعلومات</small>';
            }
        }
    }

    // حساب المبلغ المتبقي
    function calculateRemainingAmount() {
        const serviceFees = parseFloat(document.getElementById('service_fees').value) || 0;
        const paidAmount = parseFloat(document.getElementById('paid_amount').value) || 0;
        const remainingAmount = serviceFees - paidAmount;

        document.getElementById('remaining_amount').value = remainingAmount.toFixed(2);
    }

    // حساب الربح التلقائي
    // المعادلة: رسوم الخدمة - المدفوع + المبلغ المتفق مع العميل - المبلغ المتفق مع الوكيل - مصاريف أخرى
    function calculateProfit() {
        const serviceFees = parseFloat(document.getElementById('service_fees').value) || 0;
        const paidAmount = parseFloat(document.getElementById('paid_amount').value) || 0;
        const customerAmount = parseFloat(document.getElementById('customer_agreed_amount').value) || 0;
        const agentAmount = parseFloat(document.getElementById('agent_agreed_amount').value) || 0;
        const otherExpenses = parseFloat(document.getElementById('other_expenses').value) || 0;

        const profit = serviceFees - paidAmount + customerAmount - agentAmount - otherExpenses;
        document.getElementById('profit').value = profit.toFixed(2);
    }

    // إضافة مستمعي الأحداث
    if (branchSelect) {
        branchSelect.addEventListener('change', updateOrderNumber);
    }
    
    document.getElementById('service_fees').addEventListener('input', function() {
        calculateRemainingAmount();
        calculateProfit();
    });
    document.getElementById('paid_amount').addEventListener('input', function() {
        calculateRemainingAmount();
        calculateProfit();
    });
    document.getElementById('customer_agreed_amount').addEventListener('input', calculateProfit);
    document.getElementById('agent_agreed_amount').addEventListener('input', calculateProfit);
    document.getElementById('other_expenses').addEventListener('input', calculateProfit);

    // تشغيل الحسابات عند تحميل الصفحة
    updateOrderNumber(); // تشغيل دائماً سواء كان الفرع محدد أو لا
    calculateRemainingAmount();
    calculateProfit();
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp2\htdocs\app\resources\views/orders/create.blade.php ENDPATH**/ ?>