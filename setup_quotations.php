<?php
/**
 * صفحة إعداد نظام عروض الأسعار
 * تقوم بإنشاء الجدول وإدراج البيانات التجريبية
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'alahmadi_a';
$username = 'root';
$password = '';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>إعداد نظام عروض الأسعار</h1>";
    
    // إنشاء جدول عروض الأسعار
    $createTableSQL = "
    CREATE TABLE IF NOT EXISTS `quotations` (
        `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
        `quotation_number` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `customer_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `customer_phone` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `customer_email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `customer_address` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `service_type` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `goods_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `goods_type` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `country_of_origin` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `weight` decimal(10,2) DEFAULT NULL,
        `quantity` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `branch_id` bigint(20) UNSIGNED NOT NULL,
        `departure_area` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `delivery_area` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `service_fees` decimal(10,2) NOT NULL DEFAULT 0.00,
        `currency` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ريال',
        `delivery_time` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `valid_until` date DEFAULT NULL,
        `notes` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `status` enum('draft','sent','accepted','rejected','expired','converted') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft',
        `user_id` bigint(20) UNSIGNED NOT NULL,
        `user_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `created_by` bigint(20) UNSIGNED DEFAULT NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `quotations_quotation_number_unique` (`quotation_number`),
        KEY `quotations_status_created_at_index` (`status`,`created_at`),
        KEY `quotations_branch_id_created_at_index` (`branch_id`,`created_at`),
        KEY `quotations_user_id_created_at_index` (`user_id`,`created_at`),
        KEY `quotations_valid_until_index` (`valid_until`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createTableSQL);
    echo "<p style='color: green; font-size: 18px;'>✓ تم إنشاء جدول عروض الأسعار بنجاح</p>";
    
    // التحقق من وجود بيانات تجريبية
    $checkData = $pdo->query("SELECT COUNT(*) FROM quotations")->fetchColumn();
    
    if ($checkData == 0) {
        // إدراج بيانات تجريبية
        $insertSQL = "
        INSERT INTO `quotations` (
            `quotation_number`, 
            `customer_name`, 
            `customer_phone`, 
            `customer_email`,
            `goods_name`, 
            `branch_id`, 
            `service_fees`, 
            `currency`, 
            `valid_until`, 
            `status`, 
            `user_id`, 
            `user_name`, 
            `created_by`, 
            `created_at`, 
            `updated_at`
        ) VALUES 
        (
            'QT-MKL25-01', 
            'أحمد محمد علي', 
            '966501234567', 
            '<EMAIL>',
            'أجهزة إلكترونية', 
            1, 
            2500.00, 
            'ريال', 
            '2025-07-19', 
            'draft', 
            1, 
            'مدير النظام', 
            1, 
            NOW(), 
            NOW()
        ),
        (
            'QT-MKL25-02', 
            'فاطمة أحمد', 
            '966507654321', 
            '<EMAIL>',
            'مواد غذائية', 
            1, 
            1800.00, 
            'ريال', 
            '2025-07-20', 
            'sent', 
            1, 
            'مدير النظام', 
            1, 
            NOW(), 
            NOW()
        ),
        (
            'QT-MKL25-03', 
            'محمد عبدالله', 
            '966509876543', 
            '<EMAIL>',
            'قطع غيار سيارات', 
            1, 
            3200.00, 
            'ريال', 
            '2025-07-25', 
            'accepted', 
            1, 
            'مدير النظام', 
            1, 
            NOW(), 
            NOW()
        )";
        
        $pdo->exec($insertSQL);
        echo "<p style='color: green; font-size: 18px;'>✓ تم إدراج البيانات التجريبية بنجاح (3 عروض أسعار)</p>";
    } else {
        echo "<p style='color: orange; font-size: 18px;'>⚠ يوجد بالفعل $checkData عرض سعر في قاعدة البيانات</p>";
    }
    
    // عرض البيانات الموجودة
    echo "<h2>عروض الأسعار الموجودة:</h2>";
    $stmt = $pdo->query("SELECT * FROM quotations ORDER BY created_at DESC LIMIT 10");
    $quotations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($quotations) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background-color: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>رقم العرض</th>";
        echo "<th style='padding: 10px;'>اسم العميل</th>";
        echo "<th style='padding: 10px;'>البضائع</th>";
        echo "<th style='padding: 10px;'>القيمة</th>";
        echo "<th style='padding: 10px;'>الحالة</th>";
        echo "<th style='padding: 10px;'>صالح حتى</th>";
        echo "</tr>";
        
        foreach ($quotations as $quotation) {
            $statusColors = [
                'draft' => '#6c757d',
                'sent' => '#17a2b8',
                'accepted' => '#28a745',
                'rejected' => '#dc3545',
                'expired' => '#ffc107',
                'converted' => '#007bff'
            ];
            
            $statusNames = [
                'draft' => 'مسودة',
                'sent' => 'مرسل',
                'accepted' => 'مقبول',
                'rejected' => 'مرفوض',
                'expired' => 'منتهي الصلاحية',
                'converted' => 'تم تحويله لطلب'
            ];
            
            echo "<tr>";
            echo "<td style='padding: 8px;'><strong>" . htmlspecialchars($quotation['quotation_number']) . "</strong></td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($quotation['customer_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($quotation['goods_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . number_format($quotation['service_fees'], 2) . " " . htmlspecialchars($quotation['currency']) . "</td>";
            echo "<td style='padding: 8px;'><span style='background-color: " . $statusColors[$quotation['status']] . "; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;'>" . $statusNames[$quotation['status']] . "</span></td>";
            echo "<td style='padding: 8px;'>" . ($quotation['valid_until'] ? date('Y-m-d', strtotime($quotation['valid_until'])) : 'غير محدد') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>الخطوات التالية:</h2>";
    echo "<ol>";
    echo "<li>تسجيل الدخول إلى النظام</li>";
    echo "<li>الانتقال إلى <a href='/quotations' target='_blank'>قائمة عروض الأسعار</a></li>";
    echo "<li>أو إنشاء <a href='/quotations/create' target='_blank'>عرض سعر جديد</a></li>";
    echo "<li>أو العودة إلى <a href='/dashboard' target='_blank'>لوحة التحكم</a></li>";
    echo "</ol>";
    
    echo "<div style='background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 تم إعداد نظام عروض الأسعار بنجاح!</h3>";
    echo "<p>يمكنك الآن استخدام جميع وظائف عروض الأسعار:</p>";
    echo "<ul>";
    echo "<li>إنشاء عروض أسعار جديدة</li>";
    echo "<li>عرض وإدارة عروض الأسعار الموجودة</li>";
    echo "<li>تحديث حالة عروض الأسعار</li>";
    echo "<li>تحويل عروض الأسعار المقبولة إلى طلبات</li>";
    echo "<li>طباعة وتصدير عروض الأسعار</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red; font-size: 18px;'>✗ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL</li>";
    echo "<li>صحة اسم قاعدة البيانات: $dbname</li>";
    echo "<li>صحة بيانات الاتصال</li>";
    echo "</ul>";
} catch (Exception $e) {
    echo "<p style='color: red; font-size: 18px;'>✗ خطأ عام: " . $e->getMessage() . "</p>";
}
?>
