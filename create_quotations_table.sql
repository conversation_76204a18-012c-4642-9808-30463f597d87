-- إن<PERSON><PERSON>ء جدول عروض الأسعار
CREATE TABLE IF NOT EXISTS `quotations` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `quotation_number` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
    `customer_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
    `customer_phone` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `customer_email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `customer_address` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `service_type` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `goods_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
    `goods_type` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `country_of_origin` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `weight` decimal(10,2) DEFAULT NULL,
    `quantity` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `branch_id` bigint(20) UNSIGNED NOT NULL,
    `departure_area` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `delivery_area` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `service_fees` decimal(10,2) NOT NULL DEFAULT 0.00,
    `currency` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ريال',
    `delivery_time` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `valid_until` date DEFAULT NULL,
    `notes` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `status` enum('draft','sent','accepted','rejected','expired','converted') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft',
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `user_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `created_by` bigint(20) UNSIGNED DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `quotations_quotation_number_unique` (`quotation_number`),
    KEY `quotations_branch_id_foreign` (`branch_id`),
    KEY `quotations_user_id_foreign` (`user_id`),
    KEY `quotations_created_by_foreign` (`created_by`),
    KEY `quotations_status_created_at_index` (`status`,`created_at`),
    KEY `quotations_branch_id_created_at_index` (`branch_id`,`created_at`),
    KEY `quotations_user_id_created_at_index` (`user_id`,`created_at`),
    KEY `quotations_valid_until_index` (`valid_until`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة Foreign Key Constraints (اختياري - يمكن تجاهله إذا كانت الجداول المرجعية غير موجودة)
-- ALTER TABLE `quotations` ADD CONSTRAINT `quotations_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `quotations` ADD CONSTRAINT `quotations_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `quotations` ADD CONSTRAINT `quotations_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

-- إدراج بيانات تجريبية
INSERT INTO `quotations` (
    `quotation_number`, 
    `customer_name`, 
    `customer_phone`, 
    `customer_email`,
    `goods_name`, 
    `branch_id`, 
    `service_fees`, 
    `currency`, 
    `valid_until`, 
    `status`, 
    `user_id`, 
    `user_name`, 
    `created_by`, 
    `created_at`, 
    `updated_at`
) VALUES 
(
    'QT-MKL25-01', 
    'أحمد محمد علي', 
    '966501234567', 
    '<EMAIL>',
    'أجهزة إلكترونية', 
    1, 
    2500.00, 
    'ريال', 
    '2025-07-19', 
    'draft', 
    1, 
    'مدير النظام', 
    1, 
    NOW(), 
    NOW()
),
(
    'QT-MKL25-02', 
    'فاطمة أحمد', 
    '966507654321', 
    '<EMAIL>',
    'مواد غذائية', 
    1, 
    1800.00, 
    'ريال', 
    '2025-07-20', 
    'sent', 
    1, 
    'مدير النظام', 
    1, 
    NOW(), 
    NOW()
),
(
    'QT-MKL25-03', 
    'محمد عبدالله', 
    '966509876543', 
    '<EMAIL>',
    'قطع غيار سيارات', 
    1, 
    3200.00, 
    'ريال', 
    '2025-07-25', 
    'accepted', 
    1, 
    'مدير النظام', 
    1, 
    NOW(), 
    NOW()
);
