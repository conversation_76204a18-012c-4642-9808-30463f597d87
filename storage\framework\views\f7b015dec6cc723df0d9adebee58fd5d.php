

<?php $__env->startSection('title', 'إدارة الصلاحيات'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        إدارة الصلاحيات والأدوار
                    </h4>
                </div>

                <div class="card-body">
                    <!-- قسم إدارة المستخدمين -->
                    <div class="users-section">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5>قائمة المستخدمين</h5>
                                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                    <i class="fas fa-plus me-1"></i>
                                    إضافة مستخدم جديد
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>الاسم</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>القسم</th>
                                            <th>المنصب</th>
                                            <th>نوع المستخدم</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="usersTableBody">
                                        <!-- سيتم تحميل البيانات بـ JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات المستخدمين -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="totalUsers">0</h4>
                        <p class="mb-0">إجمالي المستخدمين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="activeUsers">0</h4>
                        <p class="mb-0">المستخدمين النشطين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="inactiveUsers">0</h4>
                        <p class="mb-0">المستخدمين غير النشطين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-times fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<!-- Modal إضافة مستخدم -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مستخدم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الاسم</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="email" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" name="password" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تأكيد كلمة المرور</label>
                                <input type="password" class="form-control" name="password_confirmation" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع المستخدم</label>
                                <select class="form-select" name="user_type" required>
                                    <option value="employee">موظف</option>
                                    <option value="manager">مدير قسم</option>
                                    <option value="admin">مدير النظام</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">القسم</label>
                                <input type="text" class="form-control" name="department" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المنصب</label>
                                <input type="text" class="form-control" name="position">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الفرع المخصص</label>
                                <select class="form-select" name="branch_id" id="branchSelect">
                                    <option value="">اختر الفرع</option>
                                    <!-- سيتم تحميل الفروع بـ JavaScript -->
                                </select>
                                <div class="form-text">سيتم تحديد هذا الفرع تلقائياً عند إنشاء الطلبات</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" name="is_active" checked>
                                    <label class="form-check-label">مستخدم نشط</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveUser()">حفظ</button>
            </div>
        </div>
    </div>
</div>



<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadUsers();
    loadStats();
    loadBranches();
});

function loadUsers() {
    fetch('<?php echo e(route("permissions.users")); ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayUsers(data.data);
            } else {
                console.error('خطأ في تحميل المستخدمين:', data.message);
                showAlert('خطأ في تحميل المستخدمين', 'danger');
            }
        })
        .catch(error => {
            console.error('خطأ في الشبكة:', error);
            showAlert('خطأ في الاتصال بالخادم', 'danger');
        });
}

function displayUsers(users) {
    const tbody = document.getElementById('usersTableBody');
    tbody.innerHTML = '';

    if (users.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <div class="text-muted">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <h5>لا يوجد مستخدمين</h5>
                        <p>لم يتم إنشاء أي مستخدمين بعد</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    users.forEach(user => {
        const userTypeDisplay = getUserTypeDisplay(user.user_type || user.role);
        const statusBadge = user.is_active ?
            '<span class="badge bg-success">نشط</span>' :
            '<span class="badge bg-danger">غير نشط</span>';

        const lastLogin = user.last_login_at ?
            new Date(user.last_login_at).toLocaleString('ar-SA') :
            'لم يسجل دخول';

        const row = `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar-sm me-2">
                            <div class="avatar-title bg-primary rounded-circle">
                                ${user.name.charAt(0)}
                            </div>
                        </div>
                        <div>
                            <h6 class="mb-0">${user.name}</h6>
                            <small class="text-muted">${user.role_name || 'غير محدد'}</small>
                        </div>
                    </div>
                </td>
                <td>${user.email}</td>
                <td>${user.department || '-'}</td>
                <td>${user.position || '-'}</td>
                <td>${userTypeDisplay}</td>
                <td>${statusBadge}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-primary" onclick="editUser(${user.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="manageUserPermissions(${user.id})" title="إدارة الصلاحيات">
                            <i class="fas fa-key"></i>
                        </button>
                        <button class="btn btn-sm ${user.is_active ? 'btn-secondary' : 'btn-success'}"
                                onclick="toggleUserStatus(${user.id})"
                                title="${user.is_active ? 'إلغاء التفعيل' : 'تفعيل'}">
                            <i class="fas fa-${user.is_active ? 'ban' : 'check'}"></i>
                        </button>
                        ${user.id !== 1 ? `
                            <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}



function loadStats() {
    fetch('<?php echo e(route("permissions.stats")); ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStats(data.data);
            } else {
                console.error('خطأ في تحميل الإحصائيات:', data.message);
            }
        })
        .catch(error => {
            console.error('خطأ في الشبكة:', error);
        });
}

function updateStats(stats) {
    document.getElementById('totalUsers').textContent = stats.total_users || 0;
    document.getElementById('activeUsers').textContent = stats.active_users || 0;
    document.getElementById('inactiveUsers').textContent = (stats.total_users - stats.active_users) || 0;
}

// دوال مساعدة
function getUserTypeDisplay(userType) {
    const types = {
        'admin': 'مدير النظام',
        'manager': 'مدير قسم',
        'employee': 'موظف',
        'مدير': 'مدير النظام',
        'موظف': 'موظف'
    };

    const displayName = types[userType] || userType || 'غير محدد';

    // إضافة ألوان مختلفة حسب نوع المستخدم
    let badgeClass = 'bg-secondary';
    if (userType === 'admin' || userType === 'مدير') {
        badgeClass = 'bg-danger';
    } else if (userType === 'manager') {
        badgeClass = 'bg-warning';
    } else if (userType === 'employee' || userType === 'موظف') {
        badgeClass = 'bg-primary';
    }

    return `<span class="badge ${badgeClass}">${displayName}</span>`;
}

function showAlert(message, type = 'info') {
    // إنشاء تنبيه Bootstrap
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة التنبيه في أعلى الصفحة
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// وظائف المستخدمين
function saveUser() {
    const form = document.getElementById('addUserForm');
    const formData = new FormData(form);

    // التحقق من تطابق كلمات المرور
    const password = formData.get('password');
    const passwordConfirm = formData.get('password_confirmation');

    if (password !== passwordConfirm) {
        showAlert('كلمات المرور غير متطابقة', 'danger');
        return;
    }

    // تحويل FormData إلى JSON
    const data = {};
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    // معالجة checkbox is_active بشكل صحيح
    data.is_active = formData.has('is_active');

    fetch('<?php echo e(route("permissions.users.create")); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
            form.reset();
            loadUsers(); // إعادة تحميل قائمة المستخدمين
            loadStats(); // تحديث الإحصائيات
        } else {
            showAlert(data.message, 'danger');
            if (data.errors) {
                console.error('أخطاء التحقق:', data.errors);
                // عرض أخطاء التحقق للمستخدم
                let errorMessage = 'أخطاء في البيانات المدخلة:\n';
                Object.keys(data.errors).forEach(field => {
                    errorMessage += `• ${data.errors[field].join(', ')}\n`;
                });
                showAlert(errorMessage.replace(/\n/g, '<br>'), 'danger');
            }
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('حدث خطأ أثناء إضافة المستخدم', 'danger');
    });
}

function editUser(id) {
    // جلب بيانات المستخدم
    fetch(`<?php echo e(url('/permissions/users')); ?>/${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const user = data.data;

                // ملء النموذج بالبيانات
                document.getElementById('editUserId').value = user.id;
                document.getElementById('editUserName').value = user.name;
                document.getElementById('editUserEmail').value = user.email;
                document.getElementById('editUserDepartment').value = user.department || '';
                document.getElementById('editUserPosition').value = user.position || '';
                document.getElementById('editUserActive').checked = user.is_active;

                // عرض النموذج
                new bootstrap.Modal(document.getElementById('editUserModal')).show();
            } else {
                showAlert('خطأ في جلب بيانات المستخدم', 'danger');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showAlert('خطأ في الاتصال بالخادم', 'danger');
        });
}

function deleteUser(id) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch(`<?php echo e(url('/permissions/users')); ?>/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                loadUsers(); // إعادة تحميل قائمة المستخدمين
                loadStats(); // تحديث الإحصائيات
            } else {
                showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showAlert('حدث خطأ أثناء حذف المستخدم', 'danger');
        });
    }
}

function manageUserPermissions(id) {
    // جلب صلاحيات المستخدم الحالية
    fetch(`<?php echo e(url('/permissions/users')); ?>/${id}/permissions`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('permissionsUserId').value = id;
                displayUserPermissions(data.data);
                new bootstrap.Modal(document.getElementById('userPermissionsModal')).show();
            } else {
                showAlert('خطأ في جلب صلاحيات المستخدم', 'danger');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showAlert('خطأ في الاتصال بالخادم', 'danger');
        });
}

function toggleUserStatus(userId) {
    if (confirm('هل أنت متأكد من تغيير حالة هذا المستخدم؟')) {
        fetch(`<?php echo e(url('/permissions/users')); ?>/${userId}/toggle`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                loadUsers(); // إعادة تحميل قائمة المستخدمين
            } else {
                showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showAlert('حدث خطأ أثناء تغيير حالة المستخدم', 'danger');
        });
    }
}



// وظائف إضافية للصلاحيات
function displayUserPermissions(permissionsData) {
    const container = document.getElementById('userPermissionsContent');

    let html = `
        <div class="row">
            <div class="col-12">
                <h6 class="mb-3">اختر الصلاحيات للمستخدم:</h6>
            </div>
        </div>
        <div class="row">
    `;

    const moduleNames = {
        'orders': 'الطلبات',
        'quotations': 'عروض الأسعار',
        'reports': 'التقارير',
        'users': 'المستخدمين',
        'system': 'النظام',
        'branches': 'الفروع',
        'admin_only': 'المديرين فقط'
    };

    Object.keys(permissionsData.available || {}).forEach(module => {
        const modulePermissions = permissionsData.available[module];
        const userPermissions = permissionsData.user || [];

        html += `
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            ${moduleNames[module] || module}
                        </h6>
                    </div>
                    <div class="card-body">
        `;

        modulePermissions.forEach(permission => {
            const isChecked = userPermissions.includes(permission.name) ? 'checked' : '';
            html += `
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox"
                           id="perm_${permission.name}"
                           name="permissions[]"
                           value="${permission.name}" ${isChecked}>
                    <label class="form-check-label" for="perm_${permission.name}">
                        <strong>${permission.display_name}</strong>
                        ${permission.description ? `<br><small class="text-muted">${permission.description}</small>` : ''}
                    </label>
                </div>
            `;
        });

        html += `
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

function saveUserPermissions() {
    const userId = document.getElementById('permissionsUserId').value;
    const checkboxes = document.querySelectorAll('#userPermissionsContent input[name="permissions[]"]:checked');
    const permissions = Array.from(checkboxes).map(cb => cb.value);

    fetch(`<?php echo e(url('/permissions/users')); ?>/${userId}/permissions`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ permissions: permissions })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('userPermissionsModal')).hide();
            loadUsers(); // إعادة تحميل قائمة المستخدمين
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('حدث خطأ أثناء حفظ الصلاحيات', 'danger');
    });
}

// إضافة event listeners للنماذج
document.addEventListener('DOMContentLoaded', function() {
    // نموذج إضافة مستخدم
    const addUserForm = document.getElementById('addUserForm');
    if (addUserForm) {
        addUserForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveUser();
        });
    }

    // نموذج تعديل مستخدم
    const editUserForm = document.getElementById('editUserForm');
    if (editUserForm) {
        editUserForm.addEventListener('submit', function(e) {
            e.preventDefault();
            updateUser();
        });
    }
});

function updateUser() {
    const form = document.getElementById('editUserForm');
    const formData = new FormData(form);
    const userId = document.getElementById('editUserId').value;

    // تحويل FormData إلى JSON
    const data = {};
    for (let [key, value] of formData.entries()) {
        if (key === 'is_active') {
            data[key] = document.getElementById('editUserActive').checked;
        } else if (key !== 'user_id') {
            data[key] = value;
        }
    }

    fetch(`<?php echo e(url('/permissions/users')); ?>/${userId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
            loadUsers(); // إعادة تحميل قائمة المستخدمين
        } else {
            showAlert(data.message, 'danger');
            if (data.errors) {
                console.error('أخطاء التحقق:', data.errors);
            }
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('حدث خطأ أثناء تحديث المستخدم', 'danger');
    });
}

function loadBranches() {
    fetch('<?php echo e(route("permissions.branches")); ?>')
        .then(response => response.json())
        .then(data => {
            const branchSelect = document.getElementById('branchSelect');
            if (branchSelect && data.success) {
                branchSelect.innerHTML = '<option value="">اختر الفرع</option>';
                data.data.forEach(branch => {
                    const option = document.createElement('option');
                    option.value = branch.id;
                    option.textContent = branch.name;
                    branchSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل الفروع:', error);
        });
}

</script>
<?php $__env->stopSection(); ?>

<!-- Modal تعديل المستخدم -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">تعديل المستخدم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editUserForm">
                <div class="modal-body">
                    <input type="hidden" id="editUserId" name="user_id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editUserName" class="form-label">الاسم</label>
                                <input type="text" class="form-control" id="editUserName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editUserEmail" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="editUserEmail" name="email" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editUserDepartment" class="form-label">القسم</label>
                                <input type="text" class="form-control" id="editUserDepartment" name="department">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editUserPosition" class="form-label">المنصب</label>
                                <input type="text" class="form-control" id="editUserPosition" name="position">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="editUserActive" name="is_active">
                                    <label class="form-check-label" for="editUserActive">
                                        المستخدم نشط
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editUserPassword" class="form-label">كلمة المرور الجديدة (اختياري)</label>
                        <input type="password" class="form-control" id="editUserPassword" name="password">
                        <div class="form-text">اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal إضافة مستخدم جديد -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">إضافة مستخدم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addUserForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="addUserName" class="form-label">الاسم *</label>
                                <input type="text" class="form-control" id="addUserName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="addUserEmail" class="form-label">البريد الإلكتروني *</label>
                                <input type="email" class="form-control" id="addUserEmail" name="email" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="addUserPassword" class="form-label">كلمة المرور *</label>
                                <input type="password" class="form-control" id="addUserPassword" name="password" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="addUserPasswordConfirm" class="form-label">تأكيد كلمة المرور *</label>
                                <input type="password" class="form-control" id="addUserPasswordConfirm" name="password_confirmation" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع المستخدم</label>
                                <select class="form-select" name="user_type" required>
                                    <option value="employee">موظف</option>
                                    <option value="manager">مدير قسم</option>
                                    <option value="admin">مدير النظام</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">القسم</label>
                                <input type="text" class="form-control" name="department">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المنصب</label>
                                <input type="text" class="form-control" name="position">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الفرع المخصص</label>
                                <select class="form-select" name="branch_id" id="branchSelect">
                                    <option value="">اختر الفرع</option>
                                    <!-- سيتم تحميل الفروع بـ JavaScript -->
                                </select>
                                <div class="form-text">سيتم تحديد هذا الفرع تلقائياً عند إنشاء الطلبات</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" name="is_active" checked>
                                    <label class="form-check-label">مستخدم نشط</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">إضافة المستخدم</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal إدارة صلاحيات المستخدم -->
<div class="modal fade" id="userPermissionsModal" tabindex="-1" aria-labelledby="userPermissionsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userPermissionsModalLabel">إدارة صلاحيات المستخدم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="permissionsUserId">
                <div id="userPermissionsContent">
                    <!-- سيتم تحميل المحتوى هنا -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveUserPermissions()">حفظ الصلاحيات</button>
            </div>
        </div>
    </div>
</div>





<?php $__env->startSection('styles'); ?>
<style>
    .avatar-sm {
        width: 40px;
        height: 40px;
    }

    .avatar-title {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: white;
        font-size: 16px;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        background-color: #f8f9fa;
    }

    .btn-group .btn {
        margin-right: 2px;
    }

    .badge {
        font-size: 0.75em;
    }

    .card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .nav-tabs .nav-link {
        border-radius: 0.375rem 0.375rem 0 0;
        margin-bottom: -1px;
    }

    .nav-tabs .nav-link.active {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    .table-responsive {
        border-radius: 0.375rem;
        overflow: hidden;
    }

    .btn-group .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .alert {
        border-radius: 0.5rem;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .card-header {
        border-radius: 0.375rem 0.375rem 0 0 !important;
    }

    .list-unstyled li {
        padding: 0.25rem 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .list-unstyled li:last-child {
        border-bottom: none;
    }

    .text-muted {
        font-size: 0.875rem;
    }

    .card-footer {
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
    }

    @media (max-width: 768px) {
        .btn-group {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .btn-group .btn {
            margin-right: 0;
            margin-bottom: 2px;
        }

        .table-responsive {
            font-size: 0.875rem;
        }
    }
</style>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp2\htdocs\app\resources\views/permissions/index.blade.php ENDPATH**/ ?>