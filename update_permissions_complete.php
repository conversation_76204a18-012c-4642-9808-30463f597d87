<?php
/**
 * تحديث شامل لصلاحيات عروض الأسعار
 * يقوم بإضافة الصلاحيات وربطها بالأدوار وتحديث جدول role_permissions
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'alahmadi_a';
$username = 'root';
$password = '';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>تحديث شامل لصلاحيات عروض الأسعار</h1>";
    
    // بدء المعاملة
    $pdo->beginTransaction();
    
    echo "<h2>1. إضافة صلاحيات عروض الأسعار</h2>";
    
    // حذف الصلاحيات القديمة لعروض الأسعار إن وجدت
    $pdo->exec("DELETE FROM permissions WHERE module = 'quotations'");
    echo "<p style='color: orange;'>🗑️ تم حذف الصلاحيات القديمة لعروض الأسعار</p>";
    
    // إضافة صلاحيات عروض الأسعار الجديدة
    $permissions = [
        ['create_quotations', 'إنشاء عرض سعر', 'إنشاء عروض أسعار جديدة'],
        ['view_quotations', 'عرض عروض الأسعار', 'عرض قائمة عروض الأسعار'],
        ['edit_quotations', 'تعديل عروض الأسعار', 'تعديل عروض الأسعار الموجودة'],
        ['delete_quotations', 'حذف عروض الأسعار', 'حذف عروض الأسعار'],
        ['quotations_status', 'تغيير حالة عرض السعر', 'تغيير حالة عروض الأسعار'],
        ['convert_quotations', 'تحويل عرض السعر لطلب', 'تحويل عروض الأسعار إلى طلبات']
    ];
    
    $insertPermissionSQL = "INSERT INTO `permissions` (`name`, `display_name`, `description`, `module`, `created_at`, `updated_at`) VALUES (?, ?, ?, 'quotations', NOW(), NOW())";
    $stmt = $pdo->prepare($insertPermissionSQL);
    
    foreach ($permissions as $permission) {
        $stmt->execute($permission);
        echo "<p style='color: green;'>✓ تم إضافة صلاحية: {$permission[1]} ({$permission[0]})</p>";
    }
    
    echo "<h2>2. تحديث ربط الصلاحيات بالأدوار</h2>";
    
    // حذف ربط الصلاحيات القديمة لعروض الأسعار
    $pdo->exec("DELETE FROM role_permissions WHERE permission_name LIKE '%quotation%'");
    echo "<p style='color: orange;'>🗑️ تم حذف ربط الصلاحيات القديمة</p>";
    
    // الحصول على معرفات الأدوار
    $roles = $pdo->query("SELECT id, name, display_name FROM roles")->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($roles)) {
        throw new Exception("لا توجد أدوار في قاعدة البيانات");
    }
    
    // تحديد الصلاحيات لكل دور
    $rolePermissions = [
        'admin' => [
            'create_quotations', 'view_quotations', 'edit_quotations', 
            'delete_quotations', 'quotations_status', 'convert_quotations'
        ],
        'manager' => [
            'create_quotations', 'view_quotations', 'edit_quotations', 
            'quotations_status', 'convert_quotations'
        ],
        'employee' => [
            'create_quotations', 'view_quotations', 'edit_quotations'
        ],
        'viewer' => [
            'view_quotations'
        ]
    ];
    
    $insertRolePermissionSQL = "INSERT INTO `role_permissions` (`role_id`, `permission_name`, `granted_at`, `granted_by`, `created_at`, `updated_at`) VALUES (?, ?, NOW(), 1, NOW(), NOW())";
    $stmt = $pdo->prepare($insertRolePermissionSQL);
    
    foreach ($roles as $role) {
        if (isset($rolePermissions[$role['name']])) {
            echo "<h3>دور: {$role['display_name']} ({$role['name']})</h3>";
            foreach ($rolePermissions[$role['name']] as $permissionName) {
                $stmt->execute([$role['id'], $permissionName]);
                echo "<p style='color: green; margin-left: 20px;'>✓ تم ربط صلاحية: $permissionName</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ لم يتم تحديد صلاحيات لدور: {$role['display_name']}</p>";
        }
    }
    
    // تأكيد المعاملة
    $pdo->commit();
    echo "<p style='color: green; font-size: 18px;'>✅ تم تأكيد جميع التغييرات</p>";
    
    echo "<h2>3. التحقق من النتائج</h2>";
    
    // عرض الصلاحيات المضافة
    echo "<h3>صلاحيات عروض الأسعار المضافة:</h3>";
    $quotationPermissions = $pdo->query("SELECT name, display_name, description FROM permissions WHERE module = 'quotations' ORDER BY name")->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($quotationPermissions)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background-color: #e3f2fd;'>";
        echo "<th style='padding: 10px; text-align: right;'>اسم الصلاحية</th>";
        echo "<th style='padding: 10px; text-align: right;'>الاسم المعروض</th>";
        echo "<th style='padding: 10px; text-align: right;'>الوصف</th>";
        echo "</tr>";
        
        foreach ($quotationPermissions as $permission) {
            echo "<tr>";
            echo "<td style='padding: 8px; font-family: monospace;'>" . htmlspecialchars($permission['name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($permission['display_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($permission['description']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p style='color: green;'>✅ تم إضافة " . count($quotationPermissions) . " صلاحية لعروض الأسعار</p>";
    }
    
    // عرض ربط الصلاحيات بالأدوار
    echo "<h3>ربط الصلاحيات بالأدوار:</h3>";
    $rolePermissionsResult = $pdo->query("
        SELECT r.display_name as role_name, r.name as role_code, rp.permission_name 
        FROM role_permissions rp 
        JOIN roles r ON r.id = rp.role_id 
        WHERE rp.permission_name LIKE '%quotation%'
        ORDER BY r.display_name, rp.permission_name
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($rolePermissionsResult)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background-color: #e8f5e8;'>";
        echo "<th style='padding: 10px; text-align: right;'>الدور</th>";
        echo "<th style='padding: 10px; text-align: right;'>رمز الدور</th>";
        echo "<th style='padding: 10px; text-align: right;'>الصلاحية</th>";
        echo "</tr>";
        
        foreach ($rolePermissionsResult as $rp) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($rp['role_name']) . "</td>";
            echo "<td style='padding: 8px; font-family: monospace;'>" . htmlspecialchars($rp['role_code']) . "</td>";
            echo "<td style='padding: 8px; font-family: monospace;'>" . htmlspecialchars($rp['permission_name']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p style='color: green;'>✅ تم ربط " . count($rolePermissionsResult) . " صلاحية بالأدوار</p>";
    }
    
    // إحصائيات شاملة
    echo "<h3>إحصائيات شاملة:</h3>";
    $totalPermissions = $pdo->query("SELECT COUNT(*) FROM permissions")->fetchColumn();
    $quotationPermissionsCount = $pdo->query("SELECT COUNT(*) FROM permissions WHERE module = 'quotations'")->fetchColumn();
    $totalRolePermissions = $pdo->query("SELECT COUNT(*) FROM role_permissions")->fetchColumn();
    $quotationRolePermissions = $pdo->query("SELECT COUNT(*) FROM role_permissions WHERE permission_name LIKE '%quotation%'")->fetchColumn();
    
    echo "<ul>";
    echo "<li>إجمالي الصلاحيات في النظام: <strong>$totalPermissions</strong></li>";
    echo "<li>صلاحيات عروض الأسعار: <strong>$quotationPermissionsCount</strong></li>";
    echo "<li>إجمالي ربط الصلاحيات بالأدوار: <strong>$totalRolePermissions</strong></li>";
    echo "<li>ربط صلاحيات عروض الأسعار: <strong>$quotationRolePermissions</strong></li>";
    echo "</ul>";
    
    echo "<div style='background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 تم تحديث صلاحيات عروض الأسعار بنجاح!</h3>";
    echo "<p><strong>ما تم إنجازه:</strong></p>";
    echo "<ul>";
    echo "<li>✅ إضافة 6 صلاحيات جديدة لعروض الأسعار</li>";
    echo "<li>✅ ربط الصلاحيات بجميع الأدوار حسب المستوى</li>";
    echo "<li>✅ تحديث قاعدة البيانات بنجاح</li>";
    echo "<li>✅ التحقق من صحة البيانات</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>الخطوات التالية:</h2>";
    echo "<ol>";
    echo "<li><strong>تحديث صفحة الصلاحيات:</strong> <a href='/permissions' target='_blank'>انقر هنا</a> وقم بتحديث الصفحة</li>";
    echo "<li><strong>اختبار القائمة الجانبية:</strong> تحقق من ظهور قسم عروض الأسعار</li>";
    echo "<li><strong>اختبار الوظائف:</strong>";
    echo "<ul>";
    echo "<li><a href='/quotations' target='_blank'>قائمة عروض الأسعار</a></li>";
    echo "<li><a href='/quotations/create' target='_blank'>إنشاء عرض سعر جديد</a></li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>تسجيل الخروج والدخول مرة أخرى</strong> لتحديث الصلاحيات في الجلسة</li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    $pdo->rollback();
    echo "<p style='color: red; font-size: 18px;'>✗ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    echo "<p>تم التراجع عن جميع التغييرات</p>";
} catch (Exception $e) {
    $pdo->rollback();
    echo "<p style='color: red; font-size: 18px;'>✗ خطأ عام: " . $e->getMessage() . "</p>";
    echo "<p>تم التراجع عن جميع التغييرات</p>";
}
?>
