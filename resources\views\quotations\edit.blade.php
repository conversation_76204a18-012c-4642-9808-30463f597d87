@extends('layouts.admin')

@section('title', 'تعديل عرض السعر - ' . $quotation->formatted_quotation_number)

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-edit text-warning me-2"></i>
                تعديل عرض السعر
            </h2>
            <p class="text-muted mb-0">{{ $quotation->formatted_quotation_number }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('quotations.show', $quotation) }}" class="btn btn-info">
                <i class="fas fa-eye me-2"></i>
                عرض التفاصيل
            </a>
            <a href="{{ route('quotations.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <form method="POST" action="{{ route('quotations.update', $quotation) }}">
        @csrf
        @method('PUT')
        
        <div class="row">
            <!-- معلومات العميل -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            معلومات العميل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="customer_name" class="form-label">اسم العميل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('customer_name') is-invalid @enderror" 
                                   id="customer_name" name="customer_name" 
                                   value="{{ old('customer_name', $quotation->customer_name) }}" required>
                            @error('customer_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="customer_phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control @error('customer_phone') is-invalid @enderror" 
                                   id="customer_phone" name="customer_phone" 
                                   value="{{ old('customer_phone', $quotation->customer_phone) }}">
                            @error('customer_phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="customer_email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control @error('customer_email') is-invalid @enderror" 
                                   id="customer_email" name="customer_email" 
                                   value="{{ old('customer_email', $quotation->customer_email) }}">
                            @error('customer_email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="customer_address" class="form-label">العنوان</label>
                            <textarea class="form-control @error('customer_address') is-invalid @enderror" 
                                      id="customer_address" name="customer_address" rows="3">{{ old('customer_address', $quotation->customer_address) }}</textarea>
                            @error('customer_address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            معلومات النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="quotation_number" class="form-label">رقم العرض</label>
                            <input type="text" class="form-control" id="quotation_number" 
                                   value="{{ $quotation->formatted_quotation_number }}" readonly>
                            <small class="text-muted">رقم العرض لا يمكن تعديله</small>
                        </div>

                        <div class="mb-3">
                            <label for="branch_id" class="form-label">الفرع <span class="text-danger">*</span></label>
                            <select class="form-select @error('branch_id') is-invalid @enderror" 
                                    id="branch_id" name="branch_id" required>
                                <option value="">اختر الفرع</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}" 
                                            {{ old('branch_id', $quotation->branch_id) == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('branch_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="valid_until" class="form-label">صالح حتى <span class="text-danger">*</span></label>
                            <input type="date" class="form-control @error('valid_until') is-invalid @enderror" 
                                   id="valid_until" name="valid_until" 
                                   value="{{ old('valid_until', $quotation->valid_until ? $quotation->valid_until->format('Y-m-d') : '') }}" 
                                   min="{{ date('Y-m-d', strtotime('+1 day')) }}" required>
                            @error('valid_until')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة <span class="text-danger">*</span></label>
                            <select class="form-select @error('status') is-invalid @enderror" 
                                    id="status" name="status" required>
                                <option value="draft" {{ old('status', $quotation->status) == 'draft' ? 'selected' : '' }}>مسودة</option>
                                <option value="sent" {{ old('status', $quotation->status) == 'sent' ? 'selected' : '' }}>مرسل</option>
                                <option value="accepted" {{ old('status', $quotation->status) == 'accepted' ? 'selected' : '' }}>مقبول</option>
                                <option value="rejected" {{ old('status', $quotation->status) == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                                <option value="expired" {{ old('status', $quotation->status) == 'expired' ? 'selected' : '' }}>منتهي الصلاحية</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- تفاصيل البضائع -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-boxes me-2"></i>
                            تفاصيل البضائع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="service_type" class="form-label">نوع الخدمة</label>
                            <input type="text" class="form-control @error('service_type') is-invalid @enderror" 
                                   id="service_type" name="service_type" 
                                   value="{{ old('service_type', $quotation->service_type) }}"
                                   placeholder="مثل: شحن جوي، شحن بحري، شحن بري">
                            @error('service_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="goods_name" class="form-label">اسم البضائع <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('goods_name') is-invalid @enderror" 
                                   id="goods_name" name="goods_name" 
                                   value="{{ old('goods_name', $quotation->goods_name) }}" required>
                            @error('goods_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="goods_type" class="form-label">نوع البضائع</label>
                            <input type="text" class="form-control @error('goods_type') is-invalid @enderror" 
                                   id="goods_type" name="goods_type" 
                                   value="{{ old('goods_type', $quotation->goods_type) }}">
                            @error('goods_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="country_of_origin" class="form-label">بلد المنشأ</label>
                            <input type="text" class="form-control @error('country_of_origin') is-invalid @enderror" 
                                   id="country_of_origin" name="country_of_origin" 
                                   value="{{ old('country_of_origin', $quotation->country_of_origin) }}">
                            @error('country_of_origin')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="weight" class="form-label">الوزن (كيلو)</label>
                                    <input type="number" step="0.01" class="form-control @error('weight') is-invalid @enderror" 
                                           id="weight" name="weight" 
                                           value="{{ old('weight', $quotation->weight) }}" min="0">
                                    @error('weight')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quantity" class="form-label">الكمية</label>
                                    <input type="text" class="form-control @error('quantity') is-invalid @enderror" 
                                           id="quantity" name="quantity" 
                                           value="{{ old('quantity', $quotation->quantity) }}">
                                    @error('quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تفاصيل الشحن والتسعير -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-shipping-fast me-2"></i>
                            تفاصيل الشحن والتسعير
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="departure_area" class="form-label">منطقة المغادرة</label>
                            <input type="text" class="form-control @error('departure_area') is-invalid @enderror" 
                                   id="departure_area" name="departure_area" 
                                   value="{{ old('departure_area', $quotation->departure_area) }}">
                            @error('departure_area')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="delivery_area" class="form-label">منطقة التسليم</label>
                            <input type="text" class="form-control @error('delivery_area') is-invalid @enderror" 
                                   id="delivery_area" name="delivery_area" 
                                   value="{{ old('delivery_area', $quotation->delivery_area) }}">
                            @error('delivery_area')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="delivery_time" class="form-label">مدة التسليم</label>
                            <input type="text" class="form-control @error('delivery_time') is-invalid @enderror" 
                                   id="delivery_time" name="delivery_time" 
                                   value="{{ old('delivery_time', $quotation->delivery_time) }}"
                                   placeholder="مثل: 3-5 أيام عمل">
                            @error('delivery_time')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="service_fees" class="form-label">قيمة الخدمة <span class="text-danger">*</span></label>
                                    <input type="number" step="0.01" class="form-control @error('service_fees') is-invalid @enderror" 
                                           id="service_fees" name="service_fees" 
                                           value="{{ old('service_fees', $quotation->service_fees) }}" min="0" required>
                                    @error('service_fees')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="currency" class="form-label">العملة <span class="text-danger">*</span></label>
                                    <select class="form-select @error('currency') is-invalid @enderror" 
                                            id="currency" name="currency" required>
                                        <option value="ريال" {{ old('currency', $quotation->currency) == 'ريال' ? 'selected' : '' }}>ريال</option>
                                        <option value="دولار" {{ old('currency', $quotation->currency) == 'دولار' ? 'selected' : '' }}>دولار</option>
                                        <option value="ريال سعودي" {{ old('currency', $quotation->currency) == 'ريال سعودي' ? 'selected' : '' }}>ريال سعودي</option>
                                        <option value="درهم" {{ old('currency', $quotation->currency) == 'درهم' ? 'selected' : '' }}>درهم</option>
                                    </select>
                                    @error('currency')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ملاحظات -->
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-sticky-note me-2"></i>
                    ملاحظات إضافية
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control @error('notes') is-invalid @enderror" 
                              id="notes" name="notes" rows="4" 
                              placeholder="أي ملاحظات إضافية حول عرض السعر...">{{ old('notes', $quotation->notes) }}</textarea>
                    @error('notes')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>

        <!-- أزرار الحفظ -->
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <a href="{{ route('quotations.show', $quotation) }}" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>
                        إلغاء
                    </a>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التعديلات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set minimum date for valid_until to tomorrow
        const validUntilInput = document.getElementById('valid_until');
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const minDate = tomorrow.toISOString().split('T')[0];
        validUntilInput.setAttribute('min', minDate);
    });
</script>
@endpush
