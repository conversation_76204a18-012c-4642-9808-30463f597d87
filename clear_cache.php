<?php
/**
 * مسح cache Laravel
 */

// تحديد مسار Laravel
$laravelPath = __DIR__;

// مسح ملفات cache
$cacheDirectories = [
    $laravelPath . '/bootstrap/cache',
    $laravelPath . '/storage/framework/cache',
    $laravelPath . '/storage/framework/sessions',
    $laravelPath . '/storage/framework/views'
];

echo "<h1>مسح Cache Laravel</h1>";

foreach ($cacheDirectories as $dir) {
    if (is_dir($dir)) {
        $files = glob($dir . '/*');
        $count = 0;
        foreach ($files as $file) {
            if (is_file($file) && basename($file) !== '.gitignore') {
                unlink($file);
                $count++;
            }
        }
        echo "<p>✅ تم مسح {$count} ملف من: " . basename($dir) . "</p>";
    } else {
        echo "<p>⚠️ المجلد غير موجود: {$dir}</p>";
    }
}

// مسح config cache
$configCache = $laravelPath . '/bootstrap/cache/config.php';
if (file_exists($configCache)) {
    unlink($configCache);
    echo "<p>✅ تم مسح config cache</p>";
}

// مسح route cache
$routeCache = $laravelPath . '/bootstrap/cache/routes-v7.php';
if (file_exists($routeCache)) {
    unlink($routeCache);
    echo "<p>✅ تم مسح route cache</p>";
}

echo "<h2>تم الانتهاء من مسح Cache</h2>";
echo "<p>يمكنك الآن تحديث الصفحة والمحاولة مرة أخرى.</p>";
?>
