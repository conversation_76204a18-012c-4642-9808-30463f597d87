<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Branch;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OrderController extends Controller
{
    /**
     * Display a listing of the orders.
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // إنشاء استعلام أساسي حسب صلاحيات المستخدم
        $query = $this->buildBaseQuery($user);

        // تطبيق الفلاتر
        $this->applyFilters($query, $request);

        // ترتيب النتائج وتقسيمها إلى صفحات
        $perPage = $request->get('per_page', 15);
        $orders = $query->latest()->paginate($perPage)->withQueryString();

        // جلب الفروع للفلتر (المدير يرى كل الفروع، الموظف يرى الفروع التي له طلبات فيها)
        try {
            if ($user && ($user->role === 'admin' || $user->user_type === 'admin')) {
                $branches = Branch::all();
            } else {
                $branches = Branch::whereHas('orders', function($q) use ($user) {
                    $q->where('user_id', $user->id ?? 0);
                })->get();
            }
        } catch (\Exception $e) {
            $branches = collect(); // مجموعة فارغة في حالة الخطأ
        }

        // التأكد من أن $branches ليس null
        $branches = $branches ?? collect();

        // استخدام الدالة الموحدة لحساب الإحصائيات
        $statistics = Order::getUnifiedStatistics($user);

        // حساب إحصائيات الأرباح من جميع الطلبات (حسب صلاحيات المستخدم)
        $allOrdersQuery = $statistics['base_query'];
        $profitStats = $this->calculateProfitStats($allOrdersQuery->get());

        // استخدام الإحصائيات الموحدة
        $statusStats = [
            'total_orders' => $statistics['total_orders'],
            'agreed_count' => $statistics['agreed_count'],
            'processing_count' => $statistics['processing_count'],
            'cancelled_count' => $statistics['cancelled_count']
        ];

        return view('orders.index', compact('orders', 'branches', 'profitStats', 'statusStats'));
    }

    /**
     * بناء الاستعلام الأساسي حسب صلاحيات المستخدم
     */
    private function buildBaseQuery($user)
    {
        try {
            if ($user && ($user->role === 'admin' || $user->user_type === 'admin')) {
                return Order::with(['user', 'branch']);
            } else {
                return Order::where('user_id', $user->id ?? 0)->with(['user', 'branch']);
            }
        } catch (\Exception $e) {
            return Order::where('id', 0);
        }
    }

    /**
     * حساب إحصائيات الأرباح
     */
    private function calculateProfitStats($orders)
    {
        $totalProfit = 0;
        $totalCustomerAmount = 0;
        $profitableOrders = 0;
        $lossOrders = 0;

        foreach ($orders as $order) {
            $profit = $order->profit ?? $order->calculateProfit();
            $totalProfit += $profit;
            $totalCustomerAmount += $order->customer_agreed_amount ?? 0;

            if ($profit > 0) {
                $profitableOrders++;
            } elseif ($profit < 0) {
                $lossOrders++;
            }
        }

        return [
            'total_profit' => $totalProfit,
            'total_customer_amount' => $totalCustomerAmount,
            'profitable_orders' => $profitableOrders,
            'loss_orders' => $lossOrders,
            'average_profit' => $orders->count() > 0 ? $totalProfit / $orders->count() : 0
        ];
    }



    /**
     * تطبيق الفلاتر على استعلام الطلبات
     */
    private function applyFilters($query, Request $request)
    {
        // فلتر البحث السريع
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('customer_name', 'like', "%{$search}%")
                  ->orWhere('order_number', 'like', "%{$search}%")
                  ->orWhere('branch_serial', 'like', "%{$search}%")
                  ->orWhere('phone_number', 'like', "%{$search}%")
                  ->orWhere('goods_name', 'like', "%{$search}%")
                  ->orWhere('recipient_name', 'like', "%{$search}%");
            });
        }

        // فلتر الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // فلتر الفرع
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->get('branch_id'));
        }

        // فلتر التاريخ من
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }

        // فلتر التاريخ إلى
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        // فلتر نوع الخدمة
        if ($request->filled('service_type')) {
            $query->where('service_type', 'like', '%' . $request->get('service_type') . '%');
        }

        // فلتر المبلغ (من - إلى)
        if ($request->filled('amount_from')) {
            $query->where('service_fees', '>=', $request->get('amount_from'));
        }

        if ($request->filled('amount_to')) {
            $query->where('service_fees', '<=', $request->get('amount_to'));
        }
    }

    /**
     * Show the form for creating a new order.
     */
    public function create()
    {
        $branches = Branch::all();

        // تحديد المستخدمين المتاحين بناءً على دور المستخدم الحالي
        $currentUser = Auth::user();

        // إذا كان المستخدم مدير أو مدير نظام، يمكنه رؤية جميع المستخدمين
        if ($currentUser && ($currentUser->user_type === 'admin' || $currentUser->role === 'admin' ||
                            $currentUser->user_type === 'manager' || $currentUser->role === 'manager' ||
                            $currentUser->role === 'مدير')) {
            $users = User::where('is_active', true)->get();
        } else {
            // إذا كان موظف عادي، يظهر نفسه فقط
            $users = collect([$currentUser]);
        }

        // تحديد الفرع الافتراضي بناءً على فرع المستخدم الحالي
        $defaultBranchId = null;

        // إذا كان المستخدم مرتبط بفرع معين، استخدمه كافتراضي
        if ($currentUser && $currentUser->branch_id) {
            $defaultBranchId = $currentUser->branch_id;
        }

        // تعيين الحالة الافتراضية إلى "قيد المعالجة"
        $defaultStatus = 'processing';

        return view('orders.create', compact('branches', 'users', 'defaultStatus', 'defaultBranchId'));
    }

    /**
     * Store a newly created order in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_name' => 'required|string|max:255',
            'phone_number' => 'required|string|max:20',
            'branch_id' => 'required|exists:branches,id',
            'user_id' => 'required|exists:users,id',
            'service_type' => 'required|string|max:255',
            'goods_name' => 'required|string|max:255',
            'goods_type' => 'nullable|string|max:255',
            'country_of_origin' => 'nullable|string|max:255',
            'weight' => 'required|numeric|min:0.1',
            'quantity' => 'nullable|string|max:255',
            'departure_area' => 'required|string|max:255',
            'second_interface' => 'nullable|string|max:255',
            'delivery_area' => 'required|string|max:255',
            'delivery_time' => 'nullable|string|max:255',
            'request_date' => 'nullable|date',
            'scheduled_delivery_date' => 'nullable|date',
            'recipient_name' => 'nullable|string|max:255',
            'service_fees' => 'required|numeric|min:0',
            'currency' => 'nullable|string|in:ريال,دولار,ريال سعودي,درهم',
            'paid_amount' => 'nullable|numeric|min:0',
            'remaining_amount' => 'nullable|numeric|min:0',
            'customer_agreed_amount' => 'nullable|numeric|min:0',
            'agent_agreed_amount' => 'nullable|numeric|min:0',
            'other_expenses' => 'nullable|numeric|min:0',
            'profit' => 'nullable|numeric',
            'status' => 'required|in:قيد المتابعة,تم الاتفاق,ملغي',
            'notes' => 'nullable|string|max:1000',
            'branch_serial' => 'nullable|string|max:255',
        ]);

        // سيتم إنشاء رقم الطلب تلقائياً في النموذج عبر boot method
        // لا نحتاج لإنشاءه يدوياً هنا

        // حساب المبلغ المتبقي
        $serviceFees = $validated['service_fees'] ?? 0;
        $paidAmount = $validated['paid_amount'] ?? 0;
        $validated['remaining_amount'] = $serviceFees - $paidAmount;

        // حساب الربح التلقائي
        // المعادلة: رسوم الخدمة - المدفوع + المبلغ المتفق مع العميل - المبلغ المتفق مع الوكيل - مصاريف أخرى
        $serviceFees = $validated['service_fees'] ?? 0;
        $paidAmount = $validated['paid_amount'] ?? 0;
        $customerAmount = $validated['customer_agreed_amount'] ?? 0;
        $agentAmount = $validated['agent_agreed_amount'] ?? 0;
        $otherExpenses = $validated['other_expenses'] ?? 0;
        $validated['profit'] = $serviceFees - $paidAmount + $customerAmount - $agentAmount - $otherExpenses;

        // تعيين تاريخ الطلب إلى اليوم الحالي إذا لم يتم تحديده
        if (!isset($validated['request_date']) || empty($validated['request_date'])) {
            $validated['request_date'] = now();
        }

        // تعيين المبلغ الإجمالي
        $validated['amount'] = $serviceFees;

        // إضافة اسم المستخدم
        $user = User::find($validated['user_id']);
        $validated['user_name'] = $user ? $user->name : Auth::user()->name;

        // ربط الطلب بالمستخدم الحالي
        $validated['created_by'] = Auth::id();

        Order::create($validated);

        return redirect()->route('orders.index')
            ->with('success', 'تم إنشاء الطلب بنجاح');
    }

    /**
     * Display the specified order.
     */
    public function show(Order $order)
    {
        $user = Auth::user();

        // التحقق من صلاحية عرض الطلب
        $isAdmin = $user && ($user->role === 'admin' || $user->user_type === 'admin');
        $canAccess = $user && ($order->user_id == $user->id);

        if (!$isAdmin && !$canAccess) {
            return redirect()->route('orders.index')->with('error', 'ليس لديك صلاحية لعرض هذا الطلب');
        }

        // تحميل العلاقات المطلوبة
        $order->load(['branch', 'user']);

        // التأكد من وجود معلومات الفرع
        if (!$order->branch && $order->branch_id) {
            $order->branch = Branch::find($order->branch_id);
        }

        return view('orders.show', compact('order'));
    }

    /**
     * Show the form for editing the specified order.
     */
    public function edit(Order $order)
    {
        $user = Auth::user();

        // التحقق من صلاحية تعديل الطلب
        $isAdmin = $user && ($user->role === 'admin' || $user->user_type === 'admin');
        $canAccess = $user && ($order->user_id == $user->id);

        if (!$isAdmin && !$canAccess) {
            return redirect()->route('orders.index')->with('error', 'ليس لديك صلاحية لتعديل هذا الطلب');
        }

        $branches = Branch::all();

        // تحديد المستخدمين المتاحين بناءً على دور المستخدم الحالي
        // إذا كان المستخدم مدير أو مدير نظام، يمكنه رؤية جميع المستخدمين
        if ($user && ($user->user_type === 'admin' || $user->role === 'admin' ||
                     $user->user_type === 'manager' || $user->role === 'manager' ||
                     $user->role === 'مدير')) {
            $users = User::where('is_active', true)->get();
        } else {
            // إذا كان موظف عادي، يظهر نفسه فقط
            $users = collect([$user]);
        }

        return view('orders.edit', compact('order', 'branches', 'users'));
    }

    /**
     * Update the specified order in storage.
     */
    public function update(Request $request, Order $order)
    {
        // تسجيل القيم المرسلة للتحقق من العملة
        \Log::info('Order Update Request Data:', [
            'order_id' => $order->id,
            'currency_before' => $order->currency,
            'currency_from_request' => $request->get('currency'),
            'all_request_data' => $request->all()
        ]);

        $validated = $request->validate([
            'customer_name' => 'required|string|max:255',
            'service_type' => 'nullable|string|max:255',
            'goods_name' => 'nullable|string|max:255',
            'goods_type' => 'nullable|string|max:255',
            'country_of_origin' => 'nullable|string|max:255',
            'weight' => 'nullable|numeric|min:0',
            'quantity' => 'nullable|string|max:255',
            'branch_id' => 'required|exists:branches,id',
            'departure_area' => 'nullable|string|max:255',
            'second_interface' => 'nullable|string|max:255',
            'delivery_area' => 'nullable|string|max:255',
            'service_fees' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|in:ريال,دولار,ريال سعودي,درهم',
            'delivery_time' => 'nullable|string|max:255',
            'paid_amount' => 'nullable|numeric|min:0',
            'remaining_amount' => 'nullable|numeric|min:0',
            'request_date' => 'nullable|date',
            'scheduled_delivery_date' => 'nullable|date',
            'recipient_name' => 'nullable|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'other_expenses' => 'nullable|numeric|min:0',
            'customer_agreed_amount' => 'nullable|numeric|min:0',
            'agent_agreed_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
            'status' => 'required|in:قيد المتابعة,تم الاتفاق,ملغي',
        ]);

        // الحفاظ على user_id و user_name الحاليين أو تعيينهم تلقائياً
        if (!isset($validated['user_id']) || !$validated['user_id']) {
            $validated['user_id'] = $order->user_id ?? Auth::id();
        }

        if (!isset($validated['user_name']) || !$validated['user_name']) {
            $currentUser = Auth::user();
            $validated['user_name'] = $order->user_name ?? ($currentUser ? $currentUser->name : 'غير محدد');
        }

        // تحويل القيم الفارغة إلى null للحقول الرقمية
        $numericFields = ['service_fees', 'paid_amount', 'remaining_amount', 'weight', 'other_expenses', 'customer_agreed_amount', 'agent_agreed_amount', 'profit', 'amount'];
        foreach ($numericFields as $field) {
            if (isset($validated[$field]) && $validated[$field] === '') {
                $validated[$field] = null;
            }
        }

        // التأكد من حفظ العملة بشكل صحيح
        if (!isset($validated['currency']) || empty($validated['currency'])) {
            $validated['currency'] = $order->currency ?? 'ريال';
        }

        // حساب الربح تلقائيًا
        // المعادلة: رسوم الخدمة - المدفوع + المبلغ المتفق مع العميل - المبلغ المتفق مع الوكيل - مصاريف أخرى
        $serviceFees = $validated['service_fees'] ?? $order->service_fees ?? 0;
        $paidAmount = $validated['paid_amount'] ?? $order->paid_amount ?? 0;
        $customerAmount = $validated['customer_agreed_amount'] ?? $order->customer_agreed_amount ?? 0;
        $agentAmount = $validated['agent_agreed_amount'] ?? $order->agent_agreed_amount ?? 0;
        $otherExpenses = $validated['other_expenses'] ?? $order->other_expenses ?? 0;
        $validated['profit'] = $serviceFees - $paidAmount + $customerAmount - $agentAmount - $otherExpenses;

        // حساب المبلغ المتبقي
        if (isset($validated['service_fees']) && isset($validated['paid_amount'])) {
            $validated['remaining_amount'] = $validated['service_fees'] - $validated['paid_amount'];
        }

        // تعيين المبلغ الإجمالي
        if (isset($validated['customer_agreed_amount'])) {
            $validated['amount'] = $validated['customer_agreed_amount'];
        }

        // التحقق من وجود الرقم التسلسلي للفرع
        if (!$order->branch_serial && isset($validated['branch_id'])) {
            $validated['branch_serial'] = Order::generateBranchSerialNumber($validated['branch_id']);
        }

        // تحديث الطلب مع معالجة الأخطاء
        try {
            $order->update($validated);

            // تسجيل القيم بعد التحديث
            \Log::info('Order Updated Successfully:', [
                'order_id' => $order->id,
                'currency_after' => $order->fresh()->currency,
                'validated_currency' => $validated['currency'] ?? 'not set'
            ]);

        } catch (\Exception $e) {
            \Log::error('Order Update Failed:', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'validated_data' => $validated
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث الطلب: ' . $e->getMessage());
        }

        return redirect()->route('orders.index')
            ->with('success', 'تم تحديث الطلب بنجاح');
    }

    /**
     * Remove the specified order from storage.
     */
    public function destroy(Order $order)
    {
        $order->delete();

        return redirect()->route('orders.index')
            ->with('success', 'تم حذف الطلب بنجاح');
    }

    /**
     * API للحصول على الرقم التسلسلي التالي للفرع
     */
    public function getNextSerial($branchCode)
    {
        try {
            $nextSerial = Order::getNextSerialForBranch($branchCode);
            $year = date('y');
            $orderNumber = $branchCode . $year . '-' . str_pad($nextSerial, 2, '0', STR_PAD_LEFT);

            return response()->json([
                'success' => true,
                'next_serial' => $nextSerial,
                'order_number' => $orderNumber,
                'branch_code' => $branchCode,
                'year' => $year
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في الحصول على الرقم التسلسلي: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * طباعة الطلب بصيغة PDF باستخدام TCPDF
     */
    public function printNewTemplate(Order $order)
    {
        // تحقق بسيط من تسجيل الدخول
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'يجب تسجيل الدخول أولاً');
        }

        try {
            // إنشاء مثيل TCPDF
            $pdf = new \TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

            // إعدادات الوثيقة
            $pdf->SetCreator('مجموعة أبراهيم الاحمدي اليمنية');
            $pdf->SetAuthor('مجموعة أبراهيم الاحمدي اليمنية');
            $pdf->SetTitle('طلب رقم ' . ($order->order_number ?? $order->id));

            // إعدادات الصفحة
            $pdf->SetMargins(20, 20, 20);
            $pdf->SetAutoPageBreak(TRUE, 25);
            $pdf->AddPage();

            // إضافة الشعار والمحتوى
            $this->addPdfContent($pdf, $order);

            // عرض PDF
            $filename = 'order-' . ($order->branch_serial ?? $order->id) . '.pdf';
            return response($pdf->Output($filename, 'S'), 200, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="' . $filename . '"',
            ]);

        } catch (\Exception $e) {
            \Log::error('خطأ في طباعة الطلب: ' . $e->getMessage());
            return back()->with('error', 'حدث خطأ أثناء طباعة الطلب.');
        }
    }

    /**
     * طباعة الطلب بصيغة HTML للطباعة المباشرة
     */
    public function printHtmlTemplate(Order $order)
    {
        // تحقق بسيط من تسجيل الدخول
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'يجب تسجيل الدخول أولاً');
        }

        // عرض قالب HTML للطباعة
        return view('orders.print-template-new', compact('order'));
    }

    /**
     * إضافة المحتوى إلى PDF
     */
    private function addPdfContent($pdf, $order)
    {
        // إضافة الشعار
        $logoPath = public_path('XCMYWO.png');
        if (file_exists($logoPath)) {
            $pdf->Image($logoPath, 85, 10, 40, 0, 'PNG');
        }

        $pdf->Ln(30);

        // العنوان الرئيسي
        $pdf->SetFont('dejavusans', 'B', 16);
        $pdf->Cell(0, 15, 'مجموعة إبراهيم الأحمدي اليمنية', 0, 1, 'C');
        $pdf->SetFont('dejavusans', '', 12);
        $pdf->Cell(0, 10, 'للتجارة والخدمات اللوجستية', 0, 1, 'C');
        $pdf->Ln(10);

        // معلومات الطلب
        $pdf->SetFont('dejavusans', 'B', 14);
        $pdf->Cell(0, 10, 'تفاصيل الطلب', 0, 1, 'C');
        $pdf->Ln(5);

        // جدول المعلومات
        $pdf->SetFont('dejavusans', '', 10);
        $this->addInfoRow($pdf, 'رقم الطلب:', $order->order_number ?? $order->id);
        $this->addInfoRow($pdf, 'اسم العميل:', $order->customer_name ?? 'غير محدد');
        $this->addInfoRow($pdf, 'رقم الهاتف:', $order->phone_number ?? 'غير محدد');
        $this->addInfoRow($pdf, 'نوع البضاعة:', $order->goods_name ?? 'غير محدد');
        $this->addInfoRow($pdf, 'الوزن:', ($order->weight ?? 'غير محدد') . ($order->weight ? ' كيلو' : ''));
        $this->addInfoRow($pdf, 'الكمية:', $order->quantity ?? 'غير محدد');

        $pdf->Ln(10);

        // المعلومات المالية
        $pdf->SetFont('dejavusans', 'B', 12);
        $pdf->Cell(0, 10, 'المعلومات المالية', 0, 1, 'R');
        $pdf->Ln(5);

        $pdf->SetFont('dejavusans', '', 10);
        $currency = $order->currency ?? 'ريال يمني';
        $this->addInfoRow($pdf, 'رسوم الخدمة:', number_format($order->service_fees ?? 0, 2) . ' ' . $currency);
        $this->addInfoRow($pdf, 'المبلغ المتفق عليه:', number_format($order->customer_agreed_amount ?? 0, 2) . ' ' . $currency);
        $this->addInfoRow($pdf, 'المبلغ المدفوع:', number_format($order->paid_amount ?? 0, 2) . ' ' . $currency);
        $this->addInfoRow($pdf, 'المبلغ المتبقي:', number_format($order->remaining_amount ?? 0, 2) . ' ' . $currency);

        // ملاحظات
        if ($order->notes) {
            $pdf->Ln(10);
            $pdf->SetFont('dejavusans', 'B', 12);
            $pdf->Cell(0, 10, 'ملاحظات', 0, 1, 'R');
            $pdf->Ln(5);
            $pdf->SetFont('dejavusans', '', 10);
            $pdf->MultiCell(0, 8, $order->notes, 1, 'R');
        }
    }

    /**
     * إضافة صف معلومات إلى PDF
     */
    private function addInfoRow($pdf, $label, $value)
    {
        $pdf->Cell(50, 8, $label, 1, 0, 'R');
        $pdf->Cell(120, 8, $value, 1, 1, 'R');
    }
}
