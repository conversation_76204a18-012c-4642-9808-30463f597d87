@extends('layouts.admin')

@section('title', 'تفاصيل الطلب')

@section('styles')
<style>
    @page {
        margin: 15mm 20mm;
        size: A4 portrait;
    }

    body {
        font-family: 'DejaVu Sans', 'Tahoma', 'Arial Unicode MS', sans-serif;
        background-color: #f8f9fa;
        direction: rtl;
        text-align: right;
        line-height: 1.4;
        color: #333;
    }

    .a4-container {
        width: 210mm;
        min-height: 297mm;
        margin: 20px auto;
        background: white;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        padding: 15mm;
        position: relative;
        border: 1px solid #e0e0e0;
        box-sizing: border-box;
    }

    .action-buttons {
        background: white;
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        width: 100%;
        max-width: 250mm;
        margin-left: auto;
        margin-right: auto;
    }

    .invoice-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20mm;
        padding-bottom: 5mm;
        border-bottom: 3px solid #2c3e50;
        min-height: 60px;
        position: relative;
    }

    .invoice-header::after {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(to right, #3498db, #2c3e50, #3498db);
    }

    .company-info {
        display: flex;
        align-items: center;
        gap: 10px;
        flex: 1;
    }

    .company-logo {
        width: 55px;
        height: 55px;
        object-fit: contain;
        flex-shrink: 0;
        border: 2px solid #e8e8e8;
        border-radius: 8px;
        padding: 3px;
        background: white;
    }

    .company-text {
        text-align: right;
        flex: 1;
    }

    .company-name {
        font-size: 16px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 3px;
        line-height: 1.2;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .company-subtitle {
        font-size: 12px;
        color: #7f8c8d;
        margin-bottom: 0;
        line-height: 1.2;
        font-weight: 500;
    }

    .invoice-details {
        text-align: left;
        direction: ltr;
        min-width: 140px;
        flex-shrink: 0;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 10px 15px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }

    .invoice-number {
        font-size: 16px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
        line-height: 1.2;
    }

    .invoice-date {
        font-size: 12px;
        color: #6c757d;
        line-height: 1.2;
        font-weight: 500;
    }

    .details-section {
        margin: 8mm 0;
        page-break-inside: avoid;
    }

    .section-title {
        background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
        color: white;
        border: none;
        padding: 4mm 5mm;
        font-weight: bold;
        font-size: 12px;
        margin-bottom: 0;
        text-align: center;
        border-radius: 6px 6px 0 0;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        position: relative;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 8px solid #2c3e50;
    }

    .details-table {
        width: 100%;
        border-collapse: collapse;
        border: 2px solid #3498db;
        font-size: 11px;
        margin-bottom: 5mm;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-radius: 0 0 6px 6px;
        overflow: hidden;
    }

    .details-table td {
        padding: 4mm 3mm;
        border: 1px solid #bdc3c7;
        vertical-align: middle;
        line-height: 1.4;
    }

    .details-table .label-col {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        font-weight: bold;
        width: 25%;
        color: white;
        text-align: center;
        border-right: 3px solid #3498db;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        position: relative;
    }

    .details-table .label-col::after {
        content: '';
        position: absolute;
        right: -3px;
        top: 0;
        bottom: 0;
        width: 1px;
        background: linear-gradient(to bottom, transparent 0%, #3498db 50%, transparent 100%);
    }

    .details-table .value-col {
        width: 25%;
        color: #2c3e50;
        background: #fdfdfd;
        font-weight: 600;
        position: relative;
        padding-right: 5mm;
    }

    .details-table tr:nth-child(even) .value-col {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .details-table tr:hover .value-col {
        background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
        transform: translateX(-1px);
        transition: all 0.2s ease;
    }

    /* تحسين عرض المعلومات المالية */
    .financial-highlight {
        background: linear-gradient(135deg, #e8f5e8 0%, #f0fff0 100%) !important;
        border: 2px solid #27ae60 !important;
        font-weight: bold !important;
        color: #1e7e34 !important;
        position: relative;
    }

    .financial-highlight::before {
        content: '💰';
        position: absolute;
        right: 2mm;
        top: 50%;
        transform: translateY(-50%);
        font-size: 14px;
    }

    .profit-cell {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
        border: 2px solid #28a745 !important;
        font-weight: bold !important;
        color: #155724 !important;
        text-align: center !important;
        font-size: 13px !important;
    }

    .signature-section {
        margin-top: 15mm;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        padding: 10mm 0;
        border-top: 2px solid #ecf0f1;
        page-break-inside: avoid;
    }

    .signature-box {
        text-align: center;
        width: 45mm;
        position: relative;
    }

    .signature-line {
        border-bottom: 2px solid #2c3e50;
        height: 15mm;
        margin-bottom: 3mm;
        position: relative;
        background: linear-gradient(to bottom, transparent 0%, transparent 90%, #f8f9fa 90%, #f8f9fa 100%);
    }

    .signature-line::before {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(to right, transparent 0%, #3498db 50%, transparent 100%);
    }

    .signature-label {
        font-size: 11px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 2mm;
    }

    .stamp-area {
        text-align: center;
        width: 35mm;
    }

    .stamp-circle {
        width: 30mm;
        height: 30mm;
        border: 3px dashed #3498db;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 3mm;
        font-size: 10px;
        color: #7f8c8d;
        font-weight: bold;
        background: radial-gradient(circle, #fdfdfd 0%, #f8f9fa 100%);
        position: relative;
    }

    .stamp-circle::before {
        content: '';
        position: absolute;
        width: 20mm;
        height: 20mm;
        border: 1px solid #bdc3c7;
        border-radius: 50%;
        background: transparent;
    }

    .footer-info {
        margin-top: 15mm;
        text-align: center;
        font-size: 10px;
        color: #7f8c8d;
        border-top: 2px solid #ecf0f1;
        padding-top: 8mm;
        background: linear-gradient(135deg, #fdfdfd 0%, #f8f9fa 100%);
        border-radius: 8px;
        padding: 8mm;
        line-height: 1.6;
    }

    .footer-info > div:first-child {
        font-size: 12px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 3mm;
    }

    .footer-info > div:not(:first-child):not(:last-child) {
        margin-bottom: 2mm;
    }

    .footer-info > div:last-child {
        margin-top: 5mm;
        font-size: 9px;
        color: #95a5a6;
        font-style: italic;
        border-top: 1px solid #ecf0f1;
        padding-top: 3mm;
    }

    @media print {
        body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
            margin: 0;
            padding: 0;
            background: white;
        }

        @page {
            margin: 10mm 15mm;
            size: A4 portrait;
        }

        .page-break {
            page-break-after: always;
        }

        html, body {
            width: 210mm;
            height: 297mm;
            margin: 0 !important;
            padding: 0 !important;
        }

        .wrapper {
            margin: 0 !important;
            padding: 0 !important;
        }

        .content {
            margin: 0 !important;
            padding: 0 !important;
        }

        * {
            box-sizing: border-box;
        }

        .action-buttons,
        .no-print,
        .pagination,
        .breadcrumb,
        .navbar,
        .footer-links,
        .back-button,
        .edit-button,
        .sidebar,
        .main-sidebar,
        .navbar-nav,
        .content-wrapper,
        .main-header,
        .main-footer,
        .control-sidebar,
        .navbar-expand,
        .navbar-light,
        .navbar-white,
        .layout-fixed,
        .layout-navbar-fixed,
        .layout-footer-fixed,
        .sidebar-mini,
        .nav-sidebar,
        .brand-link,
        .user-panel,
        .nav-header,
        .nav-item,
        .nav-link,
        .menu-open,
        .menu-is-opening,
        .has-treeview,
        .treeview-menu,
        .os-scrollbar,
        .os-scrollbar-horizontal,
        .os-scrollbar-vertical {
            display: none !important;
        }

        .a4-container {
            page-break-inside: avoid;
            box-shadow: none;
            margin: 0 !important;
            width: 100% !important;
            min-height: auto;
            border: none;
            padding: 5mm !important;
            background: white !important;
        }

        table, tr, td, th {
            page-break-inside: avoid !important;
            break-inside: avoid !important;
        }

        .footer-info {
            position: fixed;
            bottom: 8mm;
            left: 15mm;
            right: 15mm;
            text-align: center;
            font-size: 8px;
            background: white;
            padding: 3mm;
            border-top: 1px solid #ddd;
        }

        /* إخفاء جميع عناصر AdminLTE */
        body.sidebar-mini .main-sidebar,
        body.sidebar-mini-md .main-sidebar,
        body.sidebar-mini-xs .main-sidebar,
        .main-sidebar,
        .navbar,
        .main-header,
        .main-footer,
        .content-header,
        .breadcrumb,
        .card-header,
        .btn,
        button,
        .alert,
        .modal,
        .dropdown,
        .nav,
        .navbar-nav,
        .sidebar-dark-primary,
        .elevation-4,
        .brand-link,
        .user-panel,
        .sidebar,
        .os-host,
        .os-padding,
        .os-viewport,
        .os-content,
        .nav-pills,
        .nav-sidebar,
        .nav-flat,
        .nav-legacy,
        .nav-compact,
        .nav-child-indent,
        .layout-fixed .main-sidebar,
        .layout-navbar-fixed .main-header,
        .layout-footer-fixed .main-footer {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
        }

        /* ضمان عرض المحتوى الرئيسي فقط */
        .a4-container {
            position: relative !important;
            left: 0 !important;
            top: 0 !important;
            margin: 0 auto !important;
            width: 100% !important;
            max-width: 210mm !important;
        }
    }

    /* إخفاء عناصر إضافية في الطباعة */
    @media print {
        .wrapper {
            margin-left: 0 !important;
        }

        .content-wrapper {
            margin-left: 0 !important;
            padding: 0 !important;
        }

        body.sidebar-mini .content-wrapper,
        body.sidebar-mini-md .content-wrapper,
        body.sidebar-mini-xs .content-wrapper {
            margin-left: 0 !important;
        }
    }

    @media screen and (max-width: 768px) {
        .a4-container {
            width: 100%;
            margin: 10px;
            padding: 15px;
        }

        .invoice-header {
            flex-direction: column;
            text-align: center;
        }

        .company-info {
            justify-content: center;
            gap: 5px;
        }

        .company-logo {
            width: 35px;
            height: 35px;
        }

        .invoice-details {
            text-align: center;
            direction: rtl;
            margin-top: 10px;
        }

        .details-table .label-col,
        .details-table .value-col {
            width: 50%;
        }

        .signature-section {
            flex-direction: column;
            gap: 20px;
        }
    }
</style>
@endsection

@section('content')
<!-- Action Buttons (Outside A4 Container) -->
<div class="action-buttons">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h4 class="mb-0">تفاصيل الطلب: {{ $order->branch_serial ?? $order->order_number ?? '#' . $order->id }}</h4>
            <small class="text-muted">تم الإنشاء في: {{ $order->created_at->format('Y-m-d H:i') }}</small>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('orders.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
            <a href="{{ route('orders.edit', $order) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            @if(Route::has('orders.pdf'))
            <a href="{{ route('orders.pdf', $order) }}" class="btn btn-success">
                <i class="fas fa-file-pdf me-1"></i> تحميل PDF (TCPDF)
            </a>
            @endif
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print me-1"></i> طباعة
            </button>
        </div>
    </div>
</div>

<!-- A4 Document Container -->
<div class="a4-container">
    <!-- Invoice Header -->
    <div class="invoice-header">
        <div class="company-info">
            <img src="{{ asset('images/logos/aswsd.png') }}" alt="شعار الشركة" class="company-logo">
            <div class="company-text">
                <div class="company-name">مجموعة أبراهيم الاحمدي اليمنية</div>
                <div class="company-subtitle">للتجارة والخدمات اللوجستية</div>
            </div>
        </div>
        <div class="invoice-details">
            <div class="invoice-number">Order #{{ $order->branch_serial ?? $order->order_number ?? $order->id }}</div>
            <div class="invoice-date">{{ $order->created_at->format('Y/m/d') }}</div>
        </div>
    </div>

    <!-- Order Details Section -->
    <div class="details-section">
        <div class="section-title">معلومات الطلب</div>
        <table class="details-table">
            <tr>
                <td class="label-col">رقم الطلب</td>
                <td class="value-col">{{ $order->branch_serial ?? $order->order_number ?? $order->id }}</td>
                <td class="label-col">تاريخ الطلب</td>
                <td class="value-col">{{ $order->request_date ?? $order->created_at->format('Y-m-d') }}</td>
            </tr>
            <tr>
                <td class="label-col">اسم العميل</td>
                <td class="value-col">{{ $order->customer_name ?? 'غير محدد' }}</td>
                <td class="label-col">رقم الهاتف</td>
                <td class="value-col">{{ $order->phone_number ?? 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">اسم المستلم</td>
                <td class="value-col">{{ $order->recipient_name ?? 'غير محدد' }}</td>
                <td class="label-col">نوع الخدمة</td>
                <td class="value-col">{{ $order->service_type ?? 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">حالة الطلب</td>
                <td class="value-col">
                    @switch($order->status)
                        @case('تم الاتفاق')
                            <span style="color: #28a745; font-weight: bold; background: #d4edda; padding: 4px 8px; border-radius: 4px; border: 1px solid #c3e6cb;">
                                ✓ تم الاتفاق
                            </span>
                            @break
                        @case('قيد المتابعة')
                            <span style="color: #856404; font-weight: bold; background: #fff3cd; padding: 4px 8px; border-radius: 4px; border: 1px solid #ffeaa7;">
                                ⏳ قيد المتابعة
                            </span>
                            @break
                        @case('ملغي')
                            <span style="color: #721c24; font-weight: bold; background: #f8d7da; padding: 4px 8px; border-radius: 4px; border: 1px solid #f5c6cb;">
                                ✗ ملغي
                            </span>
                            @break
                        @default
                            <span style="color: #6c757d; font-weight: bold; background: #e9ecef; padding: 4px 8px; border-radius: 4px; border: 1px solid #ced4da;">
                                {{ $order->status ?? 'غير محدد' }}
                            </span>
                    @endswitch
                </td>
                <td class="label-col">المسؤول</td>
                <td class="value-col">{{ $order->user_name ?? ($order->user->name ?? 'غير محدد') }}</td>
            </tr>
        </table>
    </div>

    <!-- Goods Details Section -->
    <div class="details-section">
        <div class="section-title">تفاصيل البضاعة</div>
        <table class="details-table">
            <tr>
                <td class="label-col">نوع البضاعة</td>
                <td class="value-col">{{ $order->goods_name ?? 'غير محدد' }}</td>
                <td class="label-col">بلد المنشأ</td>
                <td class="value-col">{{ $order->country_of_origin ?? 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">الوزن</td>
                <td class="value-col">{{ $order->weight ?? 'غير محدد' }}</td>
                <td class="label-col">الكمية</td>
                <td class="value-col">{{ $order->quantity ?? 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">منطقة المغادرة</td>
                <td class="value-col">{{ $order->departure_area ?? 'غير محدد' }}</td>
                <td class="label-col">منطقة التسليم</td>
                <td class="value-col">{{ $order->delivery_area ?? 'غير محدد' }}</td>
            </tr>
        </table>
    </div>

    <!-- Financial Details Section -->
    <div class="details-section">
        <div class="section-title">التفاصيل المالية</div>
        <table class="details-table">
            @php
                $currencyDisplay = match($order->currency ?? 'ريال') {
                    'ريال' => 'ريال يمني',
                    'دولار' => 'دولار أمريكي',
                    'ريال سعودي' => 'ريال سعودي',
                    'درهم' => 'درهم إماراتي',
                    default => $order->currency ?? 'ريال يمني'
                };
            @endphp
            <tr>
                <td class="label-col">رسوم الخدمة</td>
                <td class="value-col">{{ $order->service_fees ? number_format($order->service_fees, 2) . ' ' . $currencyDisplay : 'غير محدد' }}</td>
                <td class="label-col">المبلغ المدفوع</td>
                <td class="value-col">{{ $order->paid_amount ? number_format($order->paid_amount, 2) . ' ' . $currencyDisplay : 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">مبلغ العميل المتفق عليه</td>
                <td class="value-col">{{ $order->customer_agreed_amount ? number_format($order->customer_agreed_amount, 2) . ' ' . $currencyDisplay : 'غير محدد' }}</td>
                <td class="label-col">المبلغ المتبقي</td>
                <td class="value-col">{{ $order->remaining_amount ? number_format($order->remaining_amount, 2) . ' ' . $currencyDisplay : 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">مبلغ الوكيل المتفق عليه</td>
                <td class="value-col">{{ $order->agent_agreed_amount ? number_format($order->agent_agreed_amount, 2) . ' ' . $currencyDisplay : 'غير محدد' }}</td>
                <td class="label-col">مصاريف أخرى</td>
                <td class="value-col">{{ $order->other_expenses ? number_format($order->other_expenses, 2) . ' ' . $currencyDisplay : 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">العملة</td>
                <td class="value-col">{{ $currencyDisplay }}</td>
                <td class="label-col" style="background: #e8f5e8; font-weight: bold;">صافي الربح</td>
                <td class="value-col" style="background: #e8f5e8; font-weight: bold; color: #27ae60;">{{ $order->profit ? number_format($order->profit, 2) . ' ' . $currencyDisplay : 'غير محدد' }}</td>
            </tr>
        </table>
    </div>

    <!-- Notes Section -->
    @if($order->notes)
    <div class="details-section">
        <div class="section-title">ملاحظات</div>
        <div style="padding: 15px; border: 1px solid #ddd; border-top: none; min-height: 60px;">
            {{ $order->notes }}
        </div>
    </div>
    @endif

    <!-- Signature Section -->
    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-line"></div>
            <div class="signature-label">توقيع العميل</div>
        </div>

        <div class="stamp-area">
            <div class="stamp-circle">
                ختم الشركة
            </div>
            <div class="signature-label">ختم الشركة</div>
        </div>

        <div class="signature-box">
            <div class="signature-line"></div>
            <div class="signature-label">توقيع المسؤول</div>
            <div style="font-size: 10px; color: #666; margin-top: 5px;">
                {{ $order->user_name ?? ($order->user->name ?? 'غير محدد') }}
            </div>
        </div>
    </div>

    <!-- Footer Information -->
    <div class="footer-info">
        <div>مجموعة أبراهيم الاحمدي اليمنية للتجارة والخدمات اللوجستية</div>
        <div>الهاتف: 738504800 | البريد الإلكتروني: <EMAIL></div>
        <div>العنوان: اليمن - حضرموت - المكلا</div>
        <div style="margin-top: 10px; font-size: 9px;">
            تم إنشاء هذا المستند في: {{ now()->format('Y-m-d H:i:s') }}
        </div>
    </div>
</div>

<script>
function printOrder() {
    window.print();
}
</script>
@endsection