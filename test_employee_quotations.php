<?php
/**
 * اختبار وصول الموظفين لعروض الأسعار
 */

// تضمين Laravel
require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

// بدء Laravel
$app->boot();

use Illuminate\Support\Facades\DB;
use App\Models\User;

echo "<h1>اختبار وصول الموظفين لعروض الأسعار</h1>";

try {
    // 1. البحث عن موظف للاختبار
    $employee = User::where('user_type', 'employee')
                   ->orWhere('role', 'employee')
                   ->first();
    
    if (!$employee) {
        echo "<p style='color: red;'>❌ لا يوجد موظفين في النظام</p>";
        echo "<p>يجب إنشاء مستخدم بنوع 'employee' أولاً</p>";
        exit;
    }
    
    echo "<h2>الموظف المختبر:</h2>";
    echo "<p><strong>الاسم:</strong> {$employee->name}</p>";
    echo "<p><strong>البريد الإلكتروني:</strong> {$employee->email}</p>";
    echo "<p><strong>نوع المستخدم:</strong> {$employee->user_type}</p>";
    echo "<p><strong>الدور:</strong> {$employee->role}</p>";
    
    // 2. التحقق من الصلاحيات المباشرة
    echo "<h2>الصلاحيات المباشرة:</h2>";
    $directPermissions = DB::table('user_permissions')
                          ->where('user_id', $employee->id)
                          ->where('permission_name', 'LIKE', '%quotation%')
                          ->pluck('permission_name')
                          ->toArray();
    
    if (empty($directPermissions)) {
        echo "<p>لا توجد صلاحيات مباشرة لعروض الأسعار</p>";
    } else {
        echo "<ul>";
        foreach ($directPermissions as $perm) {
            echo "<li>{$perm}</li>";
        }
        echo "</ul>";
    }
    
    // 3. التحقق من صلاحيات الدور
    echo "<h2>صلاحيات الدور:</h2>";
    $userRoles = DB::table('user_roles')
                  ->where('user_id', $employee->id)
                  ->pluck('role_id')
                  ->toArray();
    
    if (empty($userRoles)) {
        echo "<p>المستخدم غير مربوط بأي دور - سيتم استخدام الصلاحيات الافتراضية</p>";
        
        // الصلاحيات الافتراضية للموظفين
        $defaultPermissions = [
            'create_orders', 'view_orders', 'view_own_orders',
            'create_quotations', 'view_quotations', 'edit_quotations'
        ];
        
        echo "<h3>الصلاحيات الافتراضية للموظفين:</h3>";
        echo "<ul>";
        foreach ($defaultPermissions as $perm) {
            $color = (strpos($perm, 'quotation') !== false) ? 'green' : 'blue';
            echo "<li style='color: {$color};'>{$perm}</li>";
        }
        echo "</ul>";
        
    } else {
        $rolePermissions = DB::table('role_permissions')
                            ->whereIn('role_id', $userRoles)
                            ->where('permission_name', 'LIKE', '%quotation%')
                            ->pluck('permission_name')
                            ->toArray();
        
        if (empty($rolePermissions)) {
            echo "<p>لا توجد صلاحيات عروض أسعار مربوطة بأدوار المستخدم</p>";
        } else {
            echo "<ul>";
            foreach ($rolePermissions as $perm) {
                echo "<li style='color: green;'>{$perm}</li>";
            }
            echo "</ul>";
        }
    }
    
    // 4. اختبار الدوال المساعدة
    echo "<h2>اختبار الدوال المساعدة:</h2>";
    
    // محاكاة تسجيل دخول المستخدم
    auth()->login($employee);
    
    // اختبار hasPermission
    $quotationPermissions = [
        'view_quotations',
        'create_quotations', 
        'edit_quotations',
        'delete_quotations'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الصلاحية</th><th>النتيجة</th></tr>";
    
    foreach ($quotationPermissions as $perm) {
        $hasPermission = false;
        
        // محاولة استخدام دالة hasPermission إذا كانت موجودة
        if (function_exists('hasPermission')) {
            $hasPermission = hasPermission($perm);
        } else {
            // استخدام PermissionHelper
            if (class_exists('App\Helpers\PermissionHelper')) {
                $hasPermission = App\Helpers\PermissionHelper::hasPermission($perm);
            }
        }
        
        $result = $hasPermission ? '✅ مسموح' : '❌ غير مسموح';
        $color = $hasPermission ? 'green' : 'red';
        
        echo "<tr>";
        echo "<td>{$perm}</td>";
        echo "<td style='color: {$color};'>{$result}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 5. التوصيات
    echo "<h2>التوصيات:</h2>";
    echo "<div style='background-color: #f0f8ff; padding: 15px; border: 1px solid #0066cc; border-radius: 5px;'>";
    
    if (empty($userRoles)) {
        echo "<p style='color: orange;'><strong>تحسين:</strong> ربط المستخدم بدور 'employee' في جدول user_roles</p>";
    }
    
    echo "<p><strong>للتأكد من عمل النظام:</strong></p>";
    echo "<ol>";
    echo "<li>تسجيل الدخول كموظف: {$employee->email}</li>";
    echo "<li>التحقق من ظهور قسم 'عروض الأسعار' في القائمة الجانبية</li>";
    echo "<li>محاولة الوصول إلى: <a href='/app/public/quotations' target='_blank'>/quotations</a></li>";
    echo "<li>محاولة إنشاء عرض سعر جديد</li>";
    echo "</ol>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
