<?php
/**
 * ملف اختبار لنظام عروض الأسعار
 * يمكن تشغيله من المتصفح لاختبار الوظائف الأساسية
 */

echo "<h1>اختبار نظام عروض الأسعار</h1>";

// اختبار إنشاء جدول عروض الأسعار
echo "<h2>1. اختبار إنشاء جدول عروض الأسعار</h2>";

try {
    // محاولة الاتصال بقاعدة البيانات
    $pdo = new PDO('mysql:host=localhost;dbname=alahmadi_a', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء جدول عروض الأسعار إذا لم يكن موجوداً
    $createTableSQL = "
    CREATE TABLE IF NOT EXISTS quotations (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        quotation_number VARCHAR(191) UNIQUE NOT NULL,
        customer_name VARCHAR(191) NOT NULL,
        customer_phone VARCHAR(191) NULL,
        customer_email VARCHAR(191) NULL,
        customer_address TEXT NULL,
        service_type VARCHAR(191) NULL,
        goods_name VARCHAR(191) NOT NULL,
        goods_type VARCHAR(191) NULL,
        country_of_origin VARCHAR(191) NULL,
        weight DECIMAL(10,2) NULL,
        quantity VARCHAR(191) NULL,
        branch_id BIGINT UNSIGNED NOT NULL,
        departure_area VARCHAR(191) NULL,
        delivery_area VARCHAR(191) NULL,
        service_fees DECIMAL(10,2) DEFAULT 0,
        currency VARCHAR(191) DEFAULT 'ريال',
        delivery_time VARCHAR(191) NULL,
        valid_until DATE NULL,
        notes TEXT NULL,
        status ENUM('draft', 'sent', 'accepted', 'rejected', 'expired', 'converted') DEFAULT 'draft',
        user_id BIGINT UNSIGNED NOT NULL,
        user_name VARCHAR(191) NULL,
        created_by BIGINT UNSIGNED NULL,
        created_at TIMESTAMP NULL DEFAULT NULL,
        updated_at TIMESTAMP NULL DEFAULT NULL,
        INDEX idx_status_created (status, created_at),
        INDEX idx_branch_created (branch_id, created_at),
        INDEX idx_user_created (user_id, created_at),
        INDEX idx_valid_until (valid_until)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createTableSQL);
    echo "<p style='color: green;'>✓ تم إنشاء جدول عروض الأسعار بنجاح</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في إنشاء الجدول: " . $e->getMessage() . "</p>";
}

// اختبار إدراج بيانات تجريبية
echo "<h2>2. اختبار إدراج بيانات تجريبية</h2>";

try {
    // إدراج عرض سعر تجريبي
    $insertSQL = "
    INSERT INTO quotations (
        quotation_number, customer_name, customer_phone, goods_name, 
        branch_id, service_fees, currency, valid_until, status, 
        user_id, user_name, created_by, created_at, updated_at
    ) VALUES (
        'QT-MKL25-01', 'عميل تجريبي', '123456789', 'بضائع تجريبية',
        1, 1000.00, 'ريال', '2025-07-19', 'draft',
        1, 'مستخدم تجريبي', 1, NOW(), NOW()
    ) ON DUPLICATE KEY UPDATE updated_at = NOW();
    ";
    
    $pdo->exec($insertSQL);
    echo "<p style='color: green;'>✓ تم إدراج البيانات التجريبية بنجاح</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في إدراج البيانات: " . $e->getMessage() . "</p>";
}

// اختبار استعلام البيانات
echo "<h2>3. اختبار استعلام البيانات</h2>";

try {
    $stmt = $pdo->query("SELECT * FROM quotations LIMIT 5");
    $quotations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($quotations) > 0) {
        echo "<p style='color: green;'>✓ تم العثور على " . count($quotations) . " عرض سعر</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>رقم العرض</th><th>اسم العميل</th><th>البضائع</th><th>القيمة</th><th>الحالة</th></tr>";
        
        foreach ($quotations as $quotation) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($quotation['quotation_number']) . "</td>";
            echo "<td>" . htmlspecialchars($quotation['customer_name']) . "</td>";
            echo "<td>" . htmlspecialchars($quotation['goods_name']) . "</td>";
            echo "<td>" . number_format($quotation['service_fees'], 2) . " " . htmlspecialchars($quotation['currency']) . "</td>";
            echo "<td>" . htmlspecialchars($quotation['status']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠ لا توجد عروض أسعار في قاعدة البيانات</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في استعلام البيانات: " . $e->getMessage() . "</p>";
}

echo "<h2>4. روابط الاختبار</h2>";
echo "<ul>";
echo "<li><a href='/quotations' target='_blank'>قائمة عروض الأسعار</a></li>";
echo "<li><a href='/quotations/create' target='_blank'>إنشاء عرض سعر جديد</a></li>";
echo "<li><a href='/dashboard' target='_blank'>لوحة التحكم</a></li>";
echo "</ul>";

echo "<h2>5. ملاحظات مهمة</h2>";
echo "<ul>";
echo "<li>تأكد من وجود جدول branches وأن له بيانات</li>";
echo "<li>تأكد من وجود جدول users وأن له بيانات</li>";
echo "<li>تأكد من تسجيل الدخول قبل الوصول للصفحات</li>";
echo "<li>تأكد من وجود الصلاحيات المطلوبة للمستخدم</li>";
echo "</ul>";

echo "<p><strong>تم إنشاء نظام عروض الأسعار بنجاح! 🎉</strong></p>";
?>
