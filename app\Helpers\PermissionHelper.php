<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PermissionHelper
{
    /**
     * التحقق من صلاحية المستخدم
     */
    public static function hasPermission($permission)
    {
        if (!Auth::check()) {
            return false;
        }

        $user = Auth::user();

        // المدير الرئيسي له جميع الصلاحيات
        if ($user->id == 1 || $user->role === 'admin' || $user->user_type === 'admin') {
            return true;
        }

        // التحقق من الصلاحية المباشرة للمستخدم
        if (self::hasDirectPermission($user->id, $permission)) {
            return true;
        }

        // التحقق من صلاحيات الدور
        return self::hasRolePermission($user, $permission);
    }

    /**
     * التحقق من صلاحية المستخدم المباشرة
     */
    private static function hasDirectPermission($userId, $permission)
    {
        try {
            return DB::table('user_permissions')
                ->where('user_id', $userId)
                ->where('permission_name', $permission)
                ->exists();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من صلاحيات الدور
     */
    private static function hasRolePermission($user, $permission)
    {
        try {
            // الحصول على أدوار المستخدم
            $userRoles = DB::table('user_roles')
                ->where('user_id', $user->id)
                ->pluck('role_id');

            if ($userRoles->isEmpty()) {
                // إذا لم يكن له أدوار محددة، استخدم الدور الافتراضي
                return self::hasDefaultRolePermission($user->role ?? $user->user_type, $permission);
            }

            // التحقق من صلاحيات الأدوار
            return DB::table('role_permissions')
                ->whereIn('role_id', $userRoles)
                ->where('permission_name', $permission)
                ->exists();
        } catch (\Exception $e) {
            return self::hasDefaultRolePermission($user->role ?? $user->user_type, $permission);
        }
    }

    /**
     * التحقق من الصلاحيات الافتراضية حسب الدور
     */
    private static function hasDefaultRolePermission($role, $permission)
    {
        $defaultPermissions = [
            'admin' => [
                'create_orders', 'view_orders', 'edit_orders', 'delete_orders',
                'create_quotations', 'view_quotations', 'edit_quotations', 'delete_quotations',
                'view_reports', 'export_reports',
                'view_users', 'create_users', 'edit_users', 'delete_users',
                'manage_permissions', 'system_settings', 'backup_restore',
                'manage_branches', 'view_branches', 'create_branches', 'edit_branches'
            ],
            'manager' => [
                'create_orders', 'view_orders', 'edit_orders',
                'create_quotations', 'view_quotations', 'edit_quotations',
                'view_reports', 'export_reports',
                'view_users', 'create_users', 'edit_users',
                'view_branches'
            ],
            'employee' => [
                'create_orders', 'view_orders', 'view_own_orders',
                'create_quotations', 'view_quotations'
            ]
        ];

        return in_array($permission, $defaultPermissions[$role] ?? []);
    }

    /**
     * التحقق من عدة صلاحيات (يجب أن تكون جميعها متوفرة)
     */
    public static function hasAllPermissions(array $permissions)
    {
        foreach ($permissions as $permission) {
            if (!self::hasPermission($permission)) {
                return false;
            }
        }
        return true;
    }

    /**
     * التحقق من عدة صلاحيات (يكفي وجود واحدة منها)
     */
    public static function hasAnyPermission(array $permissions)
    {
        foreach ($permissions as $permission) {
            if (self::hasPermission($permission)) {
                return true;
            }
        }
        return false;
    }

    /**
     * التحقق من الدور
     */
    public static function hasRole($role)
    {
        if (!Auth::check()) {
            return false;
        }

        $user = Auth::user();
        return $user->role === $role || $user->user_type === $role;
    }

    /**
     * التحقق من عدة أدوار
     */
    public static function hasAnyRole(array $roles)
    {
        if (!Auth::check()) {
            return false;
        }

        $user = Auth::user();
        $userRole = $user->role ?? $user->user_type;

        return in_array($userRole, $roles);
    }

    /**
     * الحصول على جميع صلاحيات المستخدم
     */
    public static function getUserPermissions()
    {
        if (!Auth::check()) {
            return [];
        }

        $user = Auth::user();

        // المدير الرئيسي له جميع الصلاحيات
        if ($user->id == 1 || $user->role === 'admin' || $user->user_type === 'admin') {
            return [
                'create_orders', 'view_orders', 'edit_orders', 'delete_orders',
                'create_quotations', 'view_quotations', 'edit_quotations', 'delete_quotations',
                'view_reports', 'export_reports',
                'view_users', 'create_users', 'edit_users', 'delete_users',
                'manage_permissions', 'system_settings', 'backup_restore',
                'manage_branches', 'view_branches', 'create_branches', 'edit_branches'
            ];
        }

        $permissions = [];

        // جلب الصلاحيات المباشرة
        try {
            $directPermissions = DB::table('user_permissions')
                ->where('user_id', $user->id)
                ->pluck('permission_name')
                ->toArray();

            $permissions = array_merge($permissions, $directPermissions);
        } catch (\Exception $e) {
            // تجاهل الخطأ
        }

        // جلب صلاحيات الأدوار
        try {
            $userRoles = DB::table('user_roles')
                ->where('user_id', $user->id)
                ->pluck('role_id');

            if (!$userRoles->isEmpty()) {
                $rolePermissions = DB::table('role_permissions')
                    ->whereIn('role_id', $userRoles)
                    ->pluck('permission_name')
                    ->toArray();

                $permissions = array_merge($permissions, $rolePermissions);
            }
        } catch (\Exception $e) {
            // تجاهل الخطأ
        }

        // إضافة الصلاحيات الافتراضية إذا لم توجد صلاحيات
        if (empty($permissions)) {
            $role = $user->role ?? $user->user_type;
            $defaultPermissions = [
                'admin' => [
                    'create_orders', 'view_orders', 'edit_orders', 'delete_orders',
                    'create_quotations', 'view_quotations', 'edit_quotations', 'delete_quotations',
                    'view_reports', 'export_reports',
                    'view_users', 'create_users', 'edit_users', 'delete_users',
                    'manage_permissions', 'system_settings', 'backup_restore',
                    'manage_branches', 'view_branches', 'create_branches', 'edit_branches'
                ],
                'manager' => [
                    'create_orders', 'view_orders', 'edit_orders',
                    'create_quotations', 'view_quotations', 'edit_quotations',
                    'view_reports', 'export_reports',
                    'view_users', 'create_users', 'edit_users',
                    'view_branches'
                ],
                'employee' => [
                    'create_orders', 'view_orders', 'view_own_orders',
                    'create_quotations', 'view_quotations'
                ]
            ];

            $permissions = $defaultPermissions[$role] ?? [];
        }

        return array_unique($permissions);
    }
}
