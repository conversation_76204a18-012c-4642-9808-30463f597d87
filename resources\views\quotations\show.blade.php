@extends('layouts.admin')

@section('title', 'تفاصيل عرض السعر - ' . $quotation->formatted_quotation_number)

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-file-invoice-dollar text-primary me-2"></i>
                تفاصيل عرض السعر
            </h2>
            <p class="text-muted mb-0">{{ $quotation->formatted_quotation_number }}</p>
        </div>
        <div class="d-flex gap-2">
            @if(hasPermission('edit_quotations') && !in_array($quotation->status, ['accepted', 'converted']))
                <a href="{{ route('quotations.edit', $quotation) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>
                    تعديل
                </a>
            @endif
            @if($quotation->status == 'accepted' && hasPermission('create_orders'))
                <form method="POST" action="{{ route('quotations.convert-to-order', $quotation) }}" style="display: inline;">
                    @csrf
                    <button type="submit" class="btn btn-success" 
                            onclick="return confirm('هل تريد تحويل عرض السعر هذا إلى طلب؟')">
                        <i class="fas fa-exchange-alt me-2"></i>
                        تحويل لطلب
                    </button>
                </form>
            @endif
            <a href="{{ route('quotations.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <!-- Status and Actions -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-1">حالة عرض السعر</h5>
                            @php
                                $statusClasses = [
                                    'draft' => 'bg-secondary',
                                    'sent' => 'bg-info',
                                    'accepted' => 'bg-success',
                                    'rejected' => 'bg-danger',
                                    'expired' => 'bg-warning',
                                    'converted' => 'bg-primary'
                                ];
                            @endphp
                            <span class="badge {{ $statusClasses[$quotation->status] ?? 'bg-secondary' }} fs-6">
                                {{ $quotation->status_in_arabic }}
                            </span>
                            @if($quotation->isExpired())
                                <span class="badge bg-danger ms-2">منتهي الصلاحية</span>
                            @endif
                        </div>
                        @if(!in_array($quotation->status, ['accepted', 'converted']))
                            <div class="dropdown">
                                <button class="btn btn-outline-primary dropdown-toggle" type="button" 
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    تغيير الحالة
                                </button>
                                <ul class="dropdown-menu">
                                    @if($quotation->status != 'draft')
                                        <li>
                                            <form method="POST" action="{{ route('quotations.update-status', $quotation) }}" style="display: inline;">
                                                @csrf
                                                @method('PATCH')
                                                <input type="hidden" name="status" value="draft">
                                                <button type="submit" class="dropdown-item">مسودة</button>
                                            </form>
                                        </li>
                                    @endif
                                    @if($quotation->status != 'sent')
                                        <li>
                                            <form method="POST" action="{{ route('quotations.update-status', $quotation) }}" style="display: inline;">
                                                @csrf
                                                @method('PATCH')
                                                <input type="hidden" name="status" value="sent">
                                                <button type="submit" class="dropdown-item">مرسل</button>
                                            </form>
                                        </li>
                                    @endif
                                    @if($quotation->status != 'accepted')
                                        <li>
                                            <form method="POST" action="{{ route('quotations.update-status', $quotation) }}" style="display: inline;">
                                                @csrf
                                                @method('PATCH')
                                                <input type="hidden" name="status" value="accepted">
                                                <button type="submit" class="dropdown-item">مقبول</button>
                                            </form>
                                        </li>
                                    @endif
                                    @if($quotation->status != 'rejected')
                                        <li>
                                            <form method="POST" action="{{ route('quotations.update-status', $quotation) }}" style="display: inline;">
                                                @csrf
                                                @method('PATCH')
                                                <input type="hidden" name="status" value="rejected">
                                                <button type="submit" class="dropdown-item">مرفوض</button>
                                            </form>
                                        </li>
                                    @endif
                                </ul>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="mb-1">قيمة الخدمة</h5>
                    <h3 class="text-success mb-0">
                        {{ number_format($quotation->service_fees, 2) }} {{ $quotation->currency }}
                    </h3>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات العميل -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        معلومات العميل
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td class="fw-bold" width="30%">اسم العميل:</td>
                            <td>{{ $quotation->customer_name }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">رقم الهاتف:</td>
                            <td>{{ $quotation->customer_phone ?: 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">البريد الإلكتروني:</td>
                            <td>{{ $quotation->customer_email ?: 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">العنوان:</td>
                            <td>{{ $quotation->customer_address ?: 'غير محدد' }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        معلومات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td class="fw-bold" width="30%">رقم العرض:</td>
                            <td><strong class="text-primary">{{ $quotation->formatted_quotation_number }}</strong></td>
                        </tr>
                        <tr>
                            <td class="fw-bold">الفرع:</td>
                            <td>{{ $quotation->branch->name ?? 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">المستخدم المسؤول:</td>
                            <td>{{ $quotation->user->name ?? 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">صالح حتى:</td>
                            <td>
                                @if($quotation->valid_until)
                                    <span class="{{ $quotation->isExpired() ? 'text-danger' : 'text-success' }}">
                                        {{ $quotation->valid_until->format('Y-m-d') }}
                                    </span>
                                @else
                                    <span class="text-muted">غير محدد</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-bold">تاريخ الإنشاء:</td>
                            <td>{{ $quotation->created_at->format('Y-m-d H:i') }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">آخر تحديث:</td>
                            <td>{{ $quotation->updated_at->format('Y-m-d H:i') }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- تفاصيل البضائع -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-boxes me-2"></i>
                        تفاصيل البضائع
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td class="fw-bold" width="30%">نوع الخدمة:</td>
                            <td>{{ $quotation->service_type ?: 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">اسم البضائع:</td>
                            <td>{{ $quotation->goods_name }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">نوع البضائع:</td>
                            <td>{{ $quotation->goods_type ?: 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">بلد المنشأ:</td>
                            <td>{{ $quotation->country_of_origin ?: 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">الوزن:</td>
                            <td>{{ $quotation->weight ? number_format($quotation->weight, 2) . ' كيلو' : 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">الكمية:</td>
                            <td>{{ $quotation->quantity ?: 'غير محدد' }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- تفاصيل الشحن والتسعير -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-shipping-fast me-2"></i>
                        تفاصيل الشحن والتسعير
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td class="fw-bold" width="30%">منطقة المغادرة:</td>
                            <td>{{ $quotation->departure_area ?: 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">منطقة التسليم:</td>
                            <td>{{ $quotation->delivery_area ?: 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">مدة التسليم:</td>
                            <td>{{ $quotation->delivery_time ?: 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">قيمة الخدمة:</td>
                            <td>
                                <strong class="text-success fs-5">
                                    {{ number_format($quotation->service_fees, 2) }} {{ $quotation->currency }}
                                </strong>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- ملاحظات -->
    @if($quotation->notes)
    <div class="card mb-4">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">
                <i class="fas fa-sticky-note me-2"></i>
                ملاحظات
            </h5>
        </div>
        <div class="card-body">
            <p class="mb-0">{{ $quotation->notes }}</p>
        </div>
    </div>
    @endif

    <!-- Print Section -->
    <div class="card">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-print me-2"></i>
                    طباعة وتصدير
                </h5>
                <div>
                    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="exportToPDF()">
                        <i class="fas fa-file-pdf me-2"></i>
                        تصدير PDF
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Print Styles -->
<style>
@media print {
    .btn, .dropdown, .card-header .btn, .no-print {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .card-header {
        background-color: #f8f9fa !important;
        color: #000 !important;
        border-bottom: 1px solid #000 !important;
    }
    
    body {
        font-size: 12px;
    }
    
    .container-fluid {
        padding: 0;
    }
}
</style>
@endsection

@push('scripts')
<script>
    function exportToPDF() {
        // يمكن إضافة وظيفة تصدير PDF هنا
        alert('وظيفة تصدير PDF قيد التطوير');
    }
</script>
@endpush
