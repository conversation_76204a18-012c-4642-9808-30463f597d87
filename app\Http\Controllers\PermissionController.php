<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class PermissionController extends Controller
{
    /**
     * إنشاء كنترولر جديد مع التحقق من الصلاحيات
     */
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * عرض صفحة إدارة الصلاحيات
     */
    public function index()
    {
        // التحقق من الصلاحية
        if (!hasPermission('view_users') && !canManageUsers()) {
            return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية للوصول لهذه الصفحة');
        }

        return view('permissions.index');
    }

    /**
     * جلب قائمة المستخدمين
     */
    public function getUsers()
    {
        try {
            $users = DB::table('users')
                ->leftJoin('user_roles', 'users.id', '=', 'user_roles.user_id')
                ->leftJoin('roles', 'user_roles.role_id', '=', 'roles.id')
                ->select(
                    'users.*',
                    'roles.display_name as role_name'
                )
                ->get();

            return response()->json([
                'success' => true,
                'data' => $users
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب المستخدمين: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب قائمة الأدوار
     */
    public function getRoles()
    {
        try {
            $roles = DB::table('roles')
                ->leftJoin('user_roles', 'roles.id', '=', 'user_roles.role_id')
                ->leftJoin('role_permissions', 'roles.id', '=', 'role_permissions.role_id')
                ->select(
                    'roles.*',
                    DB::raw('COUNT(DISTINCT user_roles.user_id) as users_count'),
                    DB::raw('COUNT(DISTINCT role_permissions.permission_id) as permissions_count')
                )
                ->groupBy('roles.id', 'roles.name', 'roles.display_name', 'roles.description', 'roles.is_active', 'roles.created_at', 'roles.updated_at')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $roles
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب الأدوار: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب قائمة الصلاحيات
     */
    public function getPermissions()
    {
        try {
            $permissions = DB::table('permissions')
                ->select('*')
                ->orderBy('module')
                ->orderBy('display_name')
                ->get()
                ->groupBy('module');

            return response()->json([
                'success' => true,
                'data' => $permissions
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب الصلاحيات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * إنشاء مستخدم جديد
     */
    public function createUser(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email',
                'password' => 'required|string|min:6|confirmed',
                'user_type' => 'required|in:admin,manager,employee',
                'department' => 'required|string|max:255',
                'position' => 'nullable|string|max:255',
                'branch_id' => 'nullable|exists:branches,id',
                'is_active' => 'boolean'
            ]);

            DB::beginTransaction();

            // إنشاء المستخدم
            $userId = DB::table('users')->insertGetId([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'user_type' => $validated['user_type'],
                'department' => $validated['department'],
                'position' => $validated['position'],
                'branch_id' => $validated['branch_id'],
                'is_active' => $request->has('is_active'),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);

            // تحديد الدور الافتراضي بناءً على نوع المستخدم
            $defaultRoleId = match($validated['user_type']) {
                'admin' => 1,    // دور مدير النظام
                'manager' => 2,  // دور مدير القسم
                'employee' => 3, // دور الموظف
                default => 3
            };

            // التحقق من وجود الدور، وإنشاؤه إذا لم يكن موجوداً
            $role = DB::table('roles')->where('id', $defaultRoleId)->first();
            if (!$role) {
                // إنشاء الأدوار الافتراضية إذا لم تكن موجودة
                $this->createDefaultRoles();
            }

            // ربط المستخدم بالدور
            DB::table('user_roles')->insert([
                'user_id' => $userId,
                'role_id' => $defaultRoleId,
                'assigned_at' => Carbon::now(),
                'assigned_by' => Auth::id(),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء المستخدم بنجاح',
                'user_id' => $userId
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في البيانات المدخلة',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء المستخدم: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * إنشاء الأدوار الافتراضية
     */
    private function createDefaultRoles()
    {
        $roles = [
            [
                'id' => 1,
                'name' => 'admin',
                'display_name' => 'مدير النظام',
                'description' => 'صلاحيات كاملة في النظام',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            [
                'id' => 2,
                'name' => 'manager',
                'display_name' => 'مدير القسم',
                'description' => 'صلاحيات إدارية محدودة',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            [
                'id' => 3,
                'name' => 'employee',
                'display_name' => 'موظف',
                'description' => 'صلاحيات أساسية للموظفين',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]
        ];

        foreach ($roles as $role) {
            DB::table('roles')->updateOrInsert(
                ['id' => $role['id']],
                $role
            );
        }
    }

    /**
     * تحديث صلاحيات المستخدم
     */
    public function updateUserPermissions(Request $request, $userId)
    {
        try {
            $validated = $request->validate([
                'role_id' => 'required|exists:roles,id'
            ]);

            // حذف الأدوار الحالية
            DB::table('user_roles')->where('user_id', $userId)->delete();

            // إضافة الدور الجديد
            DB::table('user_roles')->insert([
                'user_id' => $userId,
                'role_id' => $validated['role_id'],
                'assigned_at' => Carbon::now(),
                'assigned_by' => Auth::id(),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث صلاحيات المستخدم بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تحديث الصلاحيات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب إحصائيات النظام
     */
    public function getStats()
    {
        try {
            $stats = [
                'total_users' => DB::table('users')->count(),
                'active_users' => DB::table('users')->where('is_active', true)->count(),
                'total_roles' => DB::table('roles')->count(),
                'total_permissions' => DB::table('permissions')->count(),
                'recent_logins' => DB::table('users')
                    ->whereNotNull('last_login_at')
                    ->where('last_login_at', '>=', Carbon::now()->subDays(7))
                    ->count()
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب الإحصائيات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تفعيل/إلغاء تفعيل المستخدم
     */
    public function toggleUserStatus($userId)
    {
        try {
            $user = DB::table('users')->where('id', $userId)->first();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'المستخدم غير موجود'
                ], 404);
            }

            // منع إلغاء تفعيل مدير النظام الأساسي
            if ($userId == 1) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن إلغاء تفعيل مدير النظام الأساسي'
                ], 403);
            }

            $newStatus = !$user->is_active;

            DB::table('users')
                ->where('id', $userId)
                ->update([
                    'is_active' => $newStatus,
                    'updated_at' => Carbon::now()
                ]);

            return response()->json([
                'success' => true,
                'message' => $newStatus ? 'تم تفعيل المستخدم' : 'تم إلغاء تفعيل المستخدم',
                'new_status' => $newStatus
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تغيير حالة المستخدم: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب بيانات مستخدم واحد
     */
    public function getUser($userId)
    {
        try {
            $user = User::findOrFail($userId);

            return response()->json([
                'success' => true,
                'data' => $user,
                'message' => 'تم جلب بيانات المستخدم بنجاح'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب بيانات المستخدم: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تحديث بيانات المستخدم
     */
    public function updateUser(Request $request, $userId)
    {
        try {
            $user = User::findOrFail($userId);

            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email,' . $userId,
                'department' => 'nullable|string|max:255',
                'position' => 'nullable|string|max:255',
                'is_active' => 'boolean',
                'password' => 'nullable|string|min:6'
            ]);

            // تحديث البيانات
            $user->name = $validated['name'];
            $user->email = $validated['email'];
            $user->department = $validated['department'];
            $user->position = $validated['position'];
            $user->is_active = $request->has('is_active') ? $validated['is_active'] : true;

            // تحديث كلمة المرور إذا تم إدخالها
            if (!empty($validated['password'])) {
                $user->password = Hash::make($validated['password']);
            }

            $user->save();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث بيانات المستخدم بنجاح',
                'data' => $user
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في البيانات المدخلة',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تحديث المستخدم: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * حذف المستخدم
     */
    public function deleteUser($userId)
    {
        try {
            $user = User::findOrFail($userId);

            // منع حذف المدير الرئيسي
            if ($user->id == 1) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف المدير الرئيسي'
                ], 403);
            }

            $user->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف المستخدم بنجاح'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في حذف المستخدم: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب صلاحيات المستخدم
     */
    public function getUserPermissions($userId)
    {
        try {
            $user = User::findOrFail($userId);

            // جلب جميع الصلاحيات المتاحة
            $availablePermissions = [
                'orders' => [
                    ['name' => 'create_orders', 'display_name' => 'إنشاء طلب جديد', 'description' => 'إمكانية إنشاء طلبات جديدة'],
                    ['name' => 'view_orders', 'display_name' => 'عرض الطلبات', 'description' => 'إمكانية عرض قائمة الطلبات'],
                    ['name' => 'edit_orders', 'display_name' => 'تعديل الطلبات', 'description' => 'إمكانية تعديل الطلبات الموجودة'],
                    ['name' => 'delete_orders', 'display_name' => 'حذف الطلبات', 'description' => 'إمكانية حذف الطلبات']
                ],
                'quotations' => [
                    ['name' => 'create_quotations', 'display_name' => 'إنشاء عرض سعر', 'description' => 'إمكانية إنشاء عروض أسعار جديدة'],
                    ['name' => 'view_quotations', 'display_name' => 'عرض عروض الأسعار', 'description' => 'إمكانية عرض قائمة عروض الأسعار'],
                    ['name' => 'edit_quotations', 'display_name' => 'تعديل عروض الأسعار', 'description' => 'إمكانية تعديل عروض الأسعار الموجودة'],
                    ['name' => 'delete_quotations', 'display_name' => 'حذف عروض الأسعار', 'description' => 'إمكانية حذف عروض الأسعار'],
                    ['name' => 'quotations_status', 'display_name' => 'تغيير حالة عرض السعر', 'description' => 'إمكانية تغيير حالة عروض الأسعار'],
                    ['name' => 'convert_quotations', 'display_name' => 'تحويل عرض السعر لطلب', 'description' => 'إمكانية تحويل عروض الأسعار إلى طلبات']
                ],
                'reports' => [
                    ['name' => 'view_reports', 'display_name' => 'عرض التقارير', 'description' => 'إمكانية عرض التقارير المختلفة'],
                    ['name' => 'export_reports', 'display_name' => 'تصدير التقارير', 'description' => 'إمكانية تصدير التقارير']
                ],
                'users' => [
                    ['name' => 'view_users', 'display_name' => 'عرض المستخدمين', 'description' => 'إمكانية عرض قائمة المستخدمين'],
                    ['name' => 'create_users', 'display_name' => 'إنشاء مستخدمين', 'description' => 'إمكانية إنشاء مستخدمين جدد'],
                    ['name' => 'edit_users', 'display_name' => 'تعديل المستخدمين', 'description' => 'إمكانية تعديل بيانات المستخدمين']
                ],
                'admin_only' => [
                    ['name' => 'system_settings', 'display_name' => 'إعدادات النظام', 'description' => 'إمكانية الوصول لإعدادات النظام'],
                    ['name' => 'backup_restore', 'display_name' => 'النسخ الاحتياطي', 'description' => 'إمكانية إنشاء واستعادة النسخ الاحتياطية']
                ]
            ];

            // جلب صلاحيات المستخدم الحالية من قاعدة البيانات
            $userPermissions = DB::table('user_permissions')
                ->where('user_id', $userId)
                ->pluck('permission_name')
                ->toArray();

            // إذا لم توجد صلاحيات محفوظة، استخدم الصلاحيات الافتراضية حسب الدور
            if (empty($userPermissions)) {
                if ($user->role === 'admin' || $user->user_type === 'admin') {
                    $userPermissions = ['create_orders', 'view_orders', 'edit_orders', 'delete_orders', 'create_quotations', 'view_quotations', 'edit_quotations', 'delete_quotations', 'quotations_status', 'convert_quotations', 'view_reports', 'export_reports', 'view_users', 'create_users', 'edit_users', 'system_settings', 'backup_restore'];
                } elseif ($user->role === 'manager' || $user->user_type === 'manager') {
                    $userPermissions = ['create_orders', 'view_orders', 'edit_orders', 'create_quotations', 'view_quotations', 'edit_quotations', 'quotations_status', 'convert_quotations', 'view_reports', 'export_reports'];
                } else {
                    $userPermissions = ['create_orders', 'view_orders', 'create_quotations', 'view_quotations', 'edit_quotations'];
                }
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'available' => $availablePermissions,
                    'user' => $userPermissions
                ],
                'message' => 'تم جلب صلاحيات المستخدم بنجاح'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب صلاحيات المستخدم: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تحديث صلاحيات المستخدم
     */
    public function updateUserPermissionsNew(Request $request, $userId)
    {
        try {
            // التحقق من وجود المستخدم
            User::findOrFail($userId);

            $validated = $request->validate([
                'permissions' => 'required|array',
                'permissions.*' => 'string'
            ]);

            DB::beginTransaction();

            // حذف الصلاحيات الحالية للمستخدم
            DB::table('user_permissions')->where('user_id', $userId)->delete();

            // إضافة الصلاحيات الجديدة
            if (!empty($validated['permissions'])) {
                $permissionData = [];
                foreach ($validated['permissions'] as $permission) {
                    $permissionData[] = [
                        'user_id' => $userId,
                        'permission_name' => $permission,
                        'granted_at' => Carbon::now(),
                        'granted_by' => Auth::id(),
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ];
                }
                DB::table('user_permissions')->insert($permissionData);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث صلاحيات المستخدم بنجاح'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تحديث صلاحيات المستخدم: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * إنشاء دور جديد
     */
    public function createRole(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:roles,name',
                'display_name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'permissions' => 'array'
            ]);

            DB::beginTransaction();

            // إنشاء الدور
            $roleId = DB::table('roles')->insertGetId([
                'name' => $validated['name'],
                'display_name' => $validated['display_name'],
                'description' => $validated['description'] ?? '',
                'is_active' => $request->has('is_active'),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);

            // ربط الصلاحيات بالدور إذا تم تحديدها
            if (!empty($validated['permissions'])) {
                $permissionData = [];
                foreach ($validated['permissions'] as $permission) {
                    $permissionData[] = [
                        'role_id' => $roleId,
                        'permission_name' => $permission,
                        'granted_at' => Carbon::now(),
                        'granted_by' => Auth::id(),
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ];
                }
                DB::table('role_permissions')->insert($permissionData);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء الدور بنجاح',
                'role_id' => $roleId
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في البيانات المدخلة',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء الدور: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب بيانات دور واحد
     */
    public function getRole($roleId)
    {
        try {
            $role = DB::table('roles')->where('id', $roleId)->first();

            if (!$role) {
                return response()->json([
                    'success' => false,
                    'message' => 'الدور غير موجود'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $role,
                'message' => 'تم جلب بيانات الدور بنجاح'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب بيانات الدور: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تحديث بيانات الدور
     */
    public function updateRole(Request $request, $roleId)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:roles,name,' . $roleId,
                'display_name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'is_active' => 'boolean'
            ]);

            $updated = DB::table('roles')
                ->where('id', $roleId)
                ->update([
                    'name' => $validated['name'],
                    'display_name' => $validated['display_name'],
                    'description' => $validated['description'] ?? '',
                    'is_active' => $request->has('is_active') ? $validated['is_active'] : true,
                    'updated_at' => Carbon::now()
                ]);

            if (!$updated) {
                return response()->json([
                    'success' => false,
                    'message' => 'الدور غير موجود'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث الدور بنجاح'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في البيانات المدخلة',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تحديث الدور: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * حذف الدور
     */
    public function deleteRole($roleId)
    {
        try {
            // منع حذف الأدوار الأساسية
            if (in_array($roleId, [1, 2, 3])) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف الأدوار الأساسية'
                ], 403);
            }

            // التحقق من وجود مستخدمين مرتبطين بهذا الدور
            $usersCount = DB::table('user_roles')->where('role_id', $roleId)->count();
            if ($usersCount > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف الدور لأنه مرتبط بمستخدمين'
                ], 403);
            }

            DB::beginTransaction();

            // حذف صلاحيات الدور
            DB::table('role_permissions')->where('role_id', $roleId)->delete();

            // حذف الدور
            $deleted = DB::table('roles')->where('id', $roleId)->delete();

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'الدور غير موجود'
                ], 404);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الدور بنجاح'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'خطأ في حذف الدور: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تغيير حالة الدور
     */
    public function toggleRoleStatus($roleId)
    {
        try {
            $role = DB::table('roles')->where('id', $roleId)->first();

            if (!$role) {
                return response()->json([
                    'success' => false,
                    'message' => 'الدور غير موجود'
                ], 404);
            }

            $newStatus = !$role->is_active;

            DB::table('roles')
                ->where('id', $roleId)
                ->update([
                    'is_active' => $newStatus,
                    'updated_at' => Carbon::now()
                ]);

            return response()->json([
                'success' => true,
                'message' => $newStatus ? 'تم تفعيل الدور' : 'تم إلغاء تفعيل الدور'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تغيير حالة الدور: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب صلاحيات الدور
     */
    public function getRolePermissions($roleId)
    {
        try {
            // جلب جميع الصلاحيات المتاحة
            $availablePermissions = [
                'orders' => [
                    ['name' => 'create_orders', 'display_name' => 'إنشاء طلب جديد', 'description' => 'إمكانية إنشاء طلبات جديدة'],
                    ['name' => 'view_orders', 'display_name' => 'عرض الطلبات', 'description' => 'إمكانية عرض قائمة الطلبات'],
                    ['name' => 'edit_orders', 'display_name' => 'تعديل الطلبات', 'description' => 'إمكانية تعديل الطلبات الموجودة'],
                    ['name' => 'delete_orders', 'display_name' => 'حذف الطلبات', 'description' => 'إمكانية حذف الطلبات']
                ],
                'quotations' => [
                    ['name' => 'create_quotations', 'display_name' => 'إنشاء عرض سعر', 'description' => 'إمكانية إنشاء عروض أسعار جديدة'],
                    ['name' => 'view_quotations', 'display_name' => 'عرض عروض الأسعار', 'description' => 'إمكانية عرض قائمة عروض الأسعار'],
                    ['name' => 'edit_quotations', 'display_name' => 'تعديل عروض الأسعار', 'description' => 'إمكانية تعديل عروض الأسعار الموجودة'],
                    ['name' => 'delete_quotations', 'display_name' => 'حذف عروض الأسعار', 'description' => 'إمكانية حذف عروض الأسعار'],
                    ['name' => 'quotations_status', 'display_name' => 'تغيير حالة عرض السعر', 'description' => 'إمكانية تغيير حالة عروض الأسعار'],
                    ['name' => 'convert_quotations', 'display_name' => 'تحويل عرض السعر لطلب', 'description' => 'إمكانية تحويل عروض الأسعار إلى طلبات']
                ],
                'reports' => [
                    ['name' => 'view_reports', 'display_name' => 'عرض التقارير', 'description' => 'إمكانية عرض التقارير المختلفة'],
                    ['name' => 'export_reports', 'display_name' => 'تصدير التقارير', 'description' => 'إمكانية تصدير التقارير']
                ],
                'users' => [
                    ['name' => 'view_users', 'display_name' => 'عرض المستخدمين', 'description' => 'إمكانية عرض قائمة المستخدمين'],
                    ['name' => 'create_users', 'display_name' => 'إنشاء مستخدمين', 'description' => 'إمكانية إنشاء مستخدمين جدد'],
                    ['name' => 'edit_users', 'display_name' => 'تعديل المستخدمين', 'description' => 'إمكانية تعديل بيانات المستخدمين']
                ],
                'admin_only' => [
                    ['name' => 'system_settings', 'display_name' => 'إعدادات النظام', 'description' => 'إمكانية الوصول لإعدادات النظام'],
                    ['name' => 'backup_restore', 'display_name' => 'النسخ الاحتياطي', 'description' => 'إمكانية إنشاء واستعادة النسخ الاحتياطية']
                ]
            ];

            // جلب صلاحيات الدور الحالية من قاعدة البيانات
            $rolePermissions = DB::table('role_permissions')
                ->where('role_id', $roleId)
                ->pluck('permission_name')
                ->toArray();

            return response()->json([
                'success' => true,
                'data' => [
                    'available' => $availablePermissions,
                    'role' => $rolePermissions
                ],
                'message' => 'تم جلب صلاحيات الدور بنجاح'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب صلاحيات الدور: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تحديث صلاحيات الدور
     */
    public function updateRolePermissions(Request $request, $roleId)
    {
        try {
            $validated = $request->validate([
                'permissions' => 'required|array',
                'permissions.*' => 'string'
            ]);

            DB::beginTransaction();

            // حذف الصلاحيات الحالية
            DB::table('role_permissions')->where('role_id', $roleId)->delete();

            // إضافة الصلاحيات الجديدة
            if (!empty($validated['permissions'])) {
                $permissionData = [];
                foreach ($validated['permissions'] as $permission) {
                    $permissionData[] = [
                        'role_id' => $roleId,
                        'permission_name' => $permission,
                        'granted_at' => Carbon::now(),
                        'granted_by' => Auth::id(),
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ];
                }
                DB::table('role_permissions')->insert($permissionData);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث صلاحيات الدور بنجاح'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تحديث صلاحيات الدور: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * إنشاء صلاحية جديدة
     */
    public function createPermission(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:permissions,name',
                'display_name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'module' => 'required|string|max:255',
                'is_active' => 'boolean'
            ]);

            $permissionId = DB::table('permissions')->insertGetId([
                'name' => $validated['name'],
                'display_name' => $validated['display_name'],
                'description' => $validated['description'] ?? '',
                'module' => $validated['module'],
                'is_active' => $request->has('is_active'),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء الصلاحية بنجاح',
                'permission_id' => $permissionId
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في البيانات المدخلة',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء الصلاحية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب قائمة الفروع للاستخدام في إنشاء المستخدمين
     */
    public function getBranches()
    {
        try {
            $branches = DB::table('branches')
                ->select('id', 'name', 'code', 'location')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $branches,
                'timestamp' => Carbon::now()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'فشل في جلب الفروع',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
