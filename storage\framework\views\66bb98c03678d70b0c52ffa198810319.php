<?php $__env->startSection('title', 'غير مصرح - 403'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg border-0">
                <div class="card-body text-center py-5">
                    <!-- أيقونة الخطأ -->
                    <div class="error-icon mb-4">
                        <i class="fas fa-shield-alt text-danger" style="font-size: 5rem;"></i>
                    </div>

                    <!-- رقم الخطأ -->
                    <h1 class="display-1 text-danger fw-bold mb-3">403</h1>
                    
                    <!-- رسالة الخطأ -->
                    <h2 class="h3 text-dark mb-3">غير مصرح لك بالوصول</h2>
                    
                    <div class="alert alert-warning mx-auto" style="max-width: 500px;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>عذراً!</strong> هذه الصفحة مخصصة للمديرين فقط.
                        <br>
                        ليس لديك الصلاحيات المطلوبة للوصول إلى هذا المحتوى.
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="info-box p-3 bg-light rounded">
                                <i class="fas fa-user-circle text-primary mb-2" style="font-size: 2rem;"></i>
                                <h5>حسابك</h5>
                                <p class="text-muted mb-0"><?php echo e(auth()->user()->name); ?></p>
                                <small class="text-muted">
                                    <?php if(auth()->user()->isAdmin()): ?>
                                        مدير النظام
                                    <?php else: ?>
                                        موظف
                                    <?php endif; ?>
                                </small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box p-3 bg-light rounded">
                                <i class="fas fa-clock text-warning mb-2" style="font-size: 2rem;"></i>
                                <h5>الوقت</h5>
                                <p class="text-muted mb-0"><?php echo e(now()->format('H:i')); ?></p>
                                <small class="text-muted"><?php echo e(now()->format('Y-m-d')); ?></small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box p-3 bg-light rounded">
                                <i class="fas fa-home text-success mb-2" style="font-size: 2rem;"></i>
                                <h5>العودة</h5>
                                <p class="text-muted mb-0">لوحة التحكم</p>
                                <small class="text-muted">الصفحة الرئيسية</small>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="mt-4">
                        <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-home me-2"></i>
                            العودة للوحة التحكم
                        </a>
                        
                        <?php if(!auth()->user()->isAdmin()): ?>
                        <a href="<?php echo e(route('orders.index')); ?>" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-list me-2"></i>
                            طلباتي
                        </a>
                        <?php endif; ?>
                    </div>

                    <!-- رسالة مساعدة -->
                    <div class="mt-4 p-3 bg-info bg-opacity-10 rounded">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        <strong>هل تحتاج مساعدة؟</strong>
                        <br>
                        إذا كنت تعتقد أنه يجب أن يكون لديك صلاحية للوصول إلى هذه الصفحة، 
                        يرجى التواصل مع مدير النظام.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.error-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.info-box {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.info-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.card {
    border-radius: 15px;
    overflow: hidden;
}

.display-1 {
    font-size: 6rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp2\htdocs\app\resources\views/errors/403.blade.php ENDPATH**/ ?>