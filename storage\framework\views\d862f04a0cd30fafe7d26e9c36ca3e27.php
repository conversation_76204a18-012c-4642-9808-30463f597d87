<!DOCTYPE html>
<html lang="<?php echo e(app()->getLocale()); ?>" dir="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>"
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', __('messages.dashboard')); ?> - <?php echo e(__('messages.welcome')); ?></title>
    <!-- Bootstrap CSS (RTL/LTR based on locale) -->
    <?php if(app()->getLocale() === 'ar'): ?>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <?php else: ?>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <?php endif; ?>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <?php if(app()->getLocale() === 'ar'): ?>
        <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Cairo:wght@400;500;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <?php endif; ?>
    <!-- Custom CSS -->
    <style>
        body {
            font-family: <?php echo e(app()->getLocale() === 'ar' ? "'Tajawal', 'Cairo', sans-serif" : "'Inter', 'Roboto', sans-serif"); ?>;
            background-color: #f5f5f5;
            position: relative;
        }

        body::before {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("<?php echo e(asset('images/logos/logo1.png')); ?>") no-repeat center center;
            background-size: 1000px;
            opacity: 0.03;
            pointer-events: none;
            z-index: 1000;
        }

        .sidebar {
            background-color: #233142;
            color: white;
            min-height: 100vh;
            position: fixed;
            top: 0;
            <?php echo e(app()->getLocale() === 'ar' ? 'right: 0;' : 'left: 0;'); ?>

            width: 250px;
            z-index: 1000;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
        }

        /* تحسين شريط التمرير للمتصفحات الحديثة */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            transition: background 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* تحسين التمرير السلس */
        .sidebar {
            scroll-behavior: smooth;
        }

        /* إضافة padding للمحتوى لتجنب قطع العناصر */
        .sidebar .nav {
            padding-bottom: 3rem;
        }

        /* تحسين إضافي للتمرير */
        .sidebar .position-sticky {
            height: 100vh;
            overflow-y: auto;
            overflow-x: hidden;
        }

        /* إصلاح مشكلة التمرير في القوائم المنسدلة */
        .sidebar .nav-item.dropdown {
            position: relative;
        }

        .sidebar .nav-item.dropdown .collapse {
            position: relative;
            z-index: 1;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.7);
            padding: 0.75rem 1rem;
            margin-bottom: 0.25rem;
            border-radius: 0.25rem;
            transition: all 0.3s ease;
            position: relative;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(-3px);
        }

        /* تنسيق القائمة المنسدلة */
        .sidebar .dropdown-toggle::after {
            display: none;
        }

        .sidebar .dropdown-icon {
            transition: transform 0.3s ease;
            font-size: 0.8rem;
        }

        .sidebar .dropdown-toggle[aria-expanded="true"] .dropdown-icon,
        .sidebar .dropdown-toggle.active .dropdown-icon {
            transform: rotate(180deg);
        }

        /* تحسين تأثير النقر على القائمة المنسدلة */
        .sidebar .dropdown-toggle:active {
            background-color: rgba(255, 255, 255, 0.15);
        }

        /* تحسين مظهر القائمة النشطة */
        .sidebar .dropdown-toggle.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: #f39c12;
        }

        .sidebar .submenu-link {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
            color: rgba(255, 255, 255, 0.6);
            border-right: 2px solid transparent;
        }

        .sidebar .submenu-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.05);
            border-right-color: #f39c12;
            transform: translateX(-2px);
        }

        .sidebar .submenu-link.active {
            color: #f39c12;
            background-color: rgba(243, 156, 18, 0.1);
            border-right-color: #f39c12;
        }

        /* تحسينات إضافية للقائمة المنسدلة */
        .sidebar .submenu-divider {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            margin: 0.5rem 1rem;
        }

        .sidebar .submenu-header {
            color: rgba(255, 255, 255, 0.5);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 0.5rem 1rem 0.25rem;
            margin-top: 0.5rem;
        }

        .sidebar .nav-item .collapse {
            transition: all 0.3s ease;
        }

        .sidebar .dropdown-toggle {
            position: relative;
            cursor: pointer;
        }

        .sidebar .dropdown-toggle::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 0;
            background-color: #f39c12;
            transition: height 0.3s ease;
        }

        .sidebar .dropdown-toggle.active::before,
        .sidebar .dropdown-toggle[aria-expanded="true"]::before {
            height: 100%;
        }

        .sidebar .dropdown-icon {
            transition: transform 0.3s ease;
            font-size: 0.8rem;
        }

        .sidebar .collapse {
            transition: all 0.3s ease;
            overflow: visible;
            max-height: none;
        }

        .sidebar .collapse:not(.show) {
            display: none;
            max-height: 0;
            overflow: hidden;
        }

        .sidebar .collapse.show {
            display: block;
            animation: slideDown 0.3s ease;
            max-height: none;
            overflow: visible;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
                max-height: 0;
            }
            to {
                opacity: 1;
                transform: translateY(0);
                max-height: 1000px;
            }
        }

        /* تحسين التمرير عند فتح القوائم المنسدلة */
        .sidebar .nav-item.dropdown .collapse.show {
            margin-bottom: 1rem;
        }

        /* إصلاح خاص لقائمة الإعدادات الطويلة */
        .sidebar #settingsSubmenu.show {
            max-height: none !important;
            overflow: visible !important;
        }

        .sidebar #settingsSubmenu .nav {
            max-height: none !important;
        }

        /* تحسين إضافي لضمان التمرير السلس */
        .sidebar .nav-item.dropdown .collapse.show ul {
            max-height: none;
            overflow: visible;
        }

        /* إصلاح مشكلة التمرير في المتصفحات المختلفة */
        .sidebar {
            -webkit-overflow-scrolling: touch;
        }

        .sidebar .collapse.show {
            will-change: transform;
        }

        /* تصميم خاص لزر تسجيل الخروج */
        .sidebar .logout-link {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white !important;
            border-radius: 0.5rem;
            margin: 0.5rem 1rem;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .sidebar .logout-link:hover {
            background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
            transform: translateX(-3px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar .logout-link i {
            color: #ffc107;
            margin-left: 0.5rem;
            transition: transform 0.3s ease;
        }

        .sidebar .logout-link:hover i {
            transform: scale(1.2) rotate(-10deg);
            color: white;
        }

        /* تصميم معلومات المطور */
        .developer-info {
            padding: 1rem;
            margin: 0.5rem;
        }

        .developer-card {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            border-radius: 0.75rem;
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            border: 2px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .developer-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #3498db, #9b59b6, #e74c3c, #f39c12);
            animation: rainbow 3s linear infinite;
        }

        @keyframes rainbow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .developer-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
            border-color: rgba(52, 152, 219, 0.5);
        }

        .developer-icon {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
            transition: all 0.3s ease;
        }

        .developer-card:hover .developer-icon {
            transform: rotate(360deg) scale(1.1);
            box-shadow: 0 6px 12px rgba(52, 152, 219, 0.5);
        }

        .developer-details {
            flex: 1;
            color: white;
        }

        .developer-title {
            font-size: 0.75rem;
            color: #bdc3c7;
            margin-bottom: 0.25rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 500;
        }

        .developer-name {
            font-size: 0.9rem;
            font-weight: 600;
            color: #ecf0f1;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .sidebar .submenu-link {
            padding-left: 2.5rem !important;
            font-size: 0.9rem;
            border-left: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .sidebar .submenu-link:hover {
            border-left-color: #f39c12;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .submenu-link.active {
            border-left-color: #f39c12;
            background-color: rgba(249, 89, 89, 0.2);
        }

        .sidebar .submenu-header {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.6);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 0.5rem 1rem;
            margin-top: 0.5rem;
        }

        .sidebar .submenu-divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            margin: 0.5rem 1rem;
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: #f95959;
            box-shadow: 0 4px 8px rgba(249, 89, 89, 0.3);
        }
        .sidebar .nav-link i {
            margin-left: 0.5rem;
            transition: transform 0.3s ease;
        }
        .sidebar .nav-link:hover i {
            transform: scale(1.1);
        }

        /* تحسين خاص لرابط الإعدادات */
        .sidebar .nav-link[href*="settings"] {
            border: 1px solid transparent;
        }
        .sidebar .nav-link[href*="settings"]:hover {
            border-color: rgba(255, 255, 255, 0.3);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.2) 100%);
        }
        .sidebar .nav-link[href*="settings"].active {
            background: linear-gradient(135deg, #f95959 0%, #e74c3c 100%);
            border-color: #e74c3c;
        }
        .sidebar .nav-link[href*="settings"] i {
            color: #ffc107;
        }
        .sidebar .nav-link[href*="settings"].active i {
            color: white;
            animation: rotate 2s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* تحسين badge الجديد */
        .sidebar .nav-link .badge {
            font-size: 0.65rem !important;
            padding: 0.2rem 0.4rem;
            border-radius: 0.25rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes fadeOut {
            0% { opacity: 1; transform: scale(1); }
            100% { opacity: 0; transform: scale(0.8); }
        }

        /* تحسين الفاصل */
        .sidebar-divider {
            margin: 1rem 0;
            border: none;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        }
        .main-content {
            <?php echo e(app()->getLocale() === 'ar' ? 'margin-right: 250px;' : 'margin-left: 250px;'); ?>

            padding: 1rem 2rem;
            min-height: 100vh;
            width: calc(100% - 250px);
        }
        .topbar {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            background-color: #f95959;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 0.75rem;
        }
        .card {
            border: none;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
        }
        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            padding: 1rem;
        }
        .table th {
            font-weight: 500;
            background-color: #4e54c8;
            color: white;
            text-align: center;
            vertical-align: middle;
        }
        .table td {
            text-align: center;
            vertical-align: middle;
        }
        .badge-status {
            padding: 0.5rem 0.75rem;
            border-radius: 0.25rem;
            font-weight: 500;
            display: inline-block;
            width: 100%;
            text-align: center;
        }

        .badge-processing {
            background-color: #007bff;
            color: white;
        }
        .badge-agreed {
            background-color: #28a745;
            color: white;
        }
        .badge-cancelled {
            background-color: #dc3545;
            color: white;
        }

        .filter-bar {
            background-color: #f8f9fa;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
.table th {
            font-weight: 500;
            background-color: #4e54c8;
            color: rgb(0, 0, 0);
            text-align: center;
            vertical-align: middle;
        }
        .table td {
            text-align: center;
            vertical-align: middle;
        }
        .action-btn {
            padding: 0.25rem 0.5rem;
            margin: 0 0.1rem;
        }
        .stat-card {
            padding: 1.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(20px);
        }
        .stat-card.loaded {
            opacity: 1;
            transform: translateY(0);
        }
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }
        .stat-success {
            background-color: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }
        .stat-primary {
            background-color: rgba(0, 123, 255, 0.2);
            color: #007bff;
        }
        .stat-warning {
            background-color: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }
        .stat-info {
            background-color: rgba(23, 162, 184, 0.2);
            color: #17a2b8;
        }
        .stat-label {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .stat-change {
            font-size: 0.875rem;
        }
        .stat-change.positive {
            color: #28a745;
        }
        .stat-change.negative {
            color: #dc3545;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                right: -250px;
                width: 250px;
                height: 100vh;
                transition: right 0.3s ease;
                z-index: 1050;
            }
            .sidebar.show {
                right: 0;
            }
            .main-content {
                margin-right: 0;
                padding: 1rem;
                width: 100%;
            }
            .mobile-menu-btn {
                display: block;
                position: fixed;
                top: 1rem;
                right: 1rem;
                z-index: 1060;
                background: #233142;
                color: white;
                border: none;
                padding: 0.75rem;
                border-radius: 0.5rem;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }
            .topbar {
                margin-top: 4rem;
            }
        }

        .mobile-menu-btn {
            display: none;
        }

        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1040;
        }

        @media (max-width: 768px) {
            .sidebar-overlay.show {
                display: block;
            }

            /* إخفاء قسم إدارة الطلبات على الأجهزة المحمولة */
            .orders-management-section {
                display: none !important;
            }
        }
    </style>
    <?php echo $__env->yieldContent('styles'); ?>
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <!-- Mobile Menu Button -->
    <button class="mobile-menu-btn" id="mobile-menu-btn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebar-overlay"></div>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4 py-2">
                        <h5 class="mb-0">مدير النظام</h5>
                        <small>مدير النظام</small>
                    </div>
                    <ul class="nav flex-column">
                        <!-- لوحة التحكم الرئيسية -->
                        <li class="submenu-header"><?php echo e(__('messages.main_dashboard') ?? (app()->getLocale() === 'ar' ? 'لوحة التحكم الرئيسية' : 'Main Dashboard')); ?></li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('dashboard')); ?>">
                                <i class="fas fa-tachometer-alt"></i>
                                <?php echo e(__('messages.dashboard')); ?>

                            </a>
                        </li>

                        <!-- إدارة الطلبات -->
                        <?php if(canManageOrders()): ?>
                        <li class="submenu-divider orders-management-section"></li>
                        <li class="submenu-header orders-management-section"><?php echo e(__('messages.orders_management') ?? (app()->getLocale() === 'ar' ? 'إدارة الطلبات' : 'Orders Management')); ?></li>
                        <?php if(hasPermission('create_orders')): ?>
                        <li class="nav-item orders-management-section">
                            <a class="nav-link <?php echo e(request()->routeIs('orders.create') ? 'active' : ''); ?>" href="<?php echo e(route('orders.create')); ?>">
                                <i class="fas fa-plus-circle"></i>
                                <?php echo e(__('messages.create_new_order') ?? (app()->getLocale() === 'ar' ? 'إنشاء طلب جديد' : 'Create New Order')); ?>

                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if(hasPermission('view_orders')): ?>
                        <li class="nav-item orders-management-section">
                            <a class="nav-link <?php echo e(request()->routeIs('orders.index') ? 'active' : ''); ?>" href="<?php echo e(route('orders.index')); ?>">
                                <i class="fas fa-list"></i>
                                <?php echo e(__('messages.orders_list') ?? (app()->getLocale() === 'ar' ? 'قائمة الطلبات' : 'Orders List')); ?>

                            </a>
                        </li>
                        <?php endif; ?>
                        <?php endif; ?>

                        <!-- إدارة عروض الأسعار -->
                        <?php if(hasPermission('view_quotations') || hasPermission('create_quotations')): ?>
                        <li class="submenu-divider"></li>
                        <li class="submenu-header">إدارة عروض الأسعار</li>
                        <!-- عروض الأسعار - قائمة منسدلة -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs('quotations.*') ? 'active' : ''); ?>"
                               href="#"
                               id="quotationsDropdown"
                               role="button"
                               data-bs-target="#quotationsSubmenu"
                               aria-expanded="<?php echo e(request()->routeIs('quotations.*') ? 'true' : 'false'); ?>"
                               aria-controls="quotationsSubmenu">
                                <i class="fas fa-file-invoice-dollar"></i>
                                عروض الأسعار
                                <i class="fas fa-chevron-down ms-auto dropdown-icon"></i>
                            </a>
                            <div class="collapse <?php echo e(request()->routeIs('quotations.*') ? 'show' : ''); ?>" id="quotationsSubmenu">
                                <ul class="nav flex-column ms-3">
                                    <?php if(hasPermission('create_quotations')): ?>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link <?php echo e(request()->routeIs('quotations.create') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('quotations.create')); ?>">
                                            <i class="fas fa-plus-circle"></i>
                                            إنشاء عرض سعر جديد
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                    <?php if(hasPermission('view_quotations')): ?>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link <?php echo e(request()->routeIs('quotations.index') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('quotations.index')); ?>">
                                            <i class="fas fa-list"></i>
                                            قائمة عروض الأسعار
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                    <?php if(hasPermission('view_quotations')): ?>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link <?php echo e(request()->routeIs('quotations.show') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('quotations.index')); ?>">
                                            <i class="fas fa-eye"></i>
                                            تفاصيل عروض الأسعار
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </li>
                        <?php endif; ?>

                        <!-- إدارة الفروع -->
                        <?php if(canManageBranches()): ?>
                        <li class="submenu-divider"></li>
                        <li class="submenu-header">إدارة الفروع</li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('branches.*') ? 'active' : ''); ?>" href="<?php echo e(route('branches.index')); ?>">
                                <i class="fas fa-code-branch"></i>
                                الفروع
                            </a>
                        </li>
                        <?php endif; ?>

                        <!-- التقارير والإحصائيات -->
                        <?php if(canViewReports()): ?>
                        <li class="submenu-divider"></li>
                        <li class="submenu-header">التقارير والإحصائيات</li>
                        <!-- التقارير - قائمة منسدلة -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs('reports.*') ? 'active' : ''); ?>"
                               href="#"
                               id="reportsDropdown"
                               role="button"
                               data-bs-target="#reportsSubmenu"
                               aria-expanded="<?php echo e(request()->routeIs('reports.*') ? 'true' : 'false'); ?>"
                               aria-controls="reportsSubmenu">
                                <i class="fas fa-file-alt"></i>
                                التقارير
                                <i class="fas fa-chevron-down ms-auto dropdown-icon"></i>
                            </a>
                            <div class="collapse <?php echo e(request()->routeIs('reports.*') ? 'show' : ''); ?>" id="reportsSubmenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link <?php echo e(request()->routeIs('reports.index') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('reports.index')); ?>">
                                            <i class="fas fa-chart-bar"></i>
                                            <?php if(isAdmin()): ?>
                                                تقارير مخصصة
                                            <?php else: ?>
                                                تقاريري
                                            <?php endif; ?>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link <?php echo e(request()->routeIs('reports.weekly') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('reports.weekly')); ?>">
                                            <i class="fas fa-calendar-week"></i>
                                            <?php if(isAdmin()): ?>
                                                التقارير الأسبوعية
                                            <?php else: ?>
                                                تقريري الأسبوعي
                                            <?php endif; ?>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link <?php echo e(request()->routeIs('reports.monthly') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('reports.monthly')); ?>">
                                            <i class="fas fa-calendar-alt"></i>
                                            <?php if(isAdmin()): ?>
                                                التقارير الشهرية
                                            <?php else: ?>
                                                تقريري الشهري
                                            <?php endif; ?>
                                        </a>
                                    </li>
                                    <?php if(isAdmin()): ?>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link <?php echo e(request()->routeIs('reports.yearly') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('reports.yearly')); ?>">
                                            <i class="fas fa-calendar"></i>
                                            الكشف السنوي
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </li>
                        <?php endif; ?>

                        <!-- إعدادات النظام والإدارة -->
                        <?php if(canManageSystem()): ?>
                        <li class="submenu-divider"></li>
                        <li class="submenu-header"><?php echo e(__('messages.system_settings_management') ?? (app()->getLocale() === 'ar' ? 'إعدادات النظام والإدارة' : 'System Settings & Management')); ?></li>
                        <!-- الإعدادات - قائمة منسدلة -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs('settings.*') ? 'active' : ''); ?>"
                               href="#"
                               id="settingsDropdown"
                               role="button"
                               data-bs-target="#settingsSubmenu"
                               aria-expanded="<?php echo e(request()->routeIs('settings.*') ? 'true' : 'false'); ?>"
                               aria-controls="settingsSubmenu">
                                <i class="fas fa-cogs"></i>
                                <?php echo e(__('messages.system_settings') ?? (app()->getLocale() === 'ar' ? 'إعدادات النظام' : 'System Settings')); ?>

                                <i class="fas fa-chevron-down ms-auto dropdown-icon"></i>
                            </a>
                            <div class="collapse <?php echo e(request()->routeIs('settings.*') ? 'show' : ''); ?>" id="settingsSubmenu">
                                <ul class="nav flex-column ms-3">
                                    <!-- الإعدادات الأساسية -->
                                    <li class="submenu-header"><?php echo e(__('messages.basic_settings') ?? (app()->getLocale() === 'ar' ? 'الإعدادات الأساسية' : 'Basic Settings')); ?></li>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link <?php echo e(request()->routeIs('settings.index') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('settings.index')); ?>">
                                            <i class="fas fa-cog"></i>
                                            <?php echo e(__('messages.general_settings') ?? (app()->getLocale() === 'ar' ? 'الإعدادات العامة' : 'General Settings')); ?>

                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link <?php echo e(request()->routeIs('settings.system') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('settings.system')); ?>">
                                            <i class="fas fa-sliders-h"></i>
                                            معاملات النظام
                                        </a>
                                    </li>

                                    <!-- إدارة المستخدمين والصلاحيات -->
                                    <?php if(hasPermission('manage_permissions')): ?>
                                    <li class="submenu-divider"></li>
                                    <li class="submenu-header"><?php echo e(__('messages.users_permissions_management')); ?></li>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link <?php echo e(request()->routeIs('permissions.*') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('permissions.index')); ?>">
                                            <i class="fas fa-shield-alt"></i>
                                            <?php echo e(__('messages.permissions_management')); ?>

                                            <span class="badge bg-primary ms-2"><?php echo e(app()->getLocale() === 'ar' ? 'شامل' : 'Full'); ?></span>
                                        </a>
                                    </li>
                                    <?php endif; ?>

                                    <!-- الصيانة والمراقبة -->
                                    <?php if(hasPermission('backup_restore') || hasPermission('system_settings')): ?>
                                    <li class="submenu-divider"></li>
                                    <li class="submenu-header">الصيانة والمراقبة</li>
                                    <?php if(hasPermission('backup_restore')): ?>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link <?php echo e(request()->routeIs('settings.backup') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('settings.backup')); ?>">
                                            <i class="fas fa-database"></i>
                                            النسخ الاحتياطية
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                    <?php if(hasPermission('system_settings')): ?>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link <?php echo e(request()->routeIs('settings.activity') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('settings.activity')); ?>">
                                            <i class="fas fa-history"></i>
                                            سجلات الأنشطة
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link <?php echo e(request()->routeIs('settings.models') ? 'active' : ''); ?>"
                                           href="<?php echo e(route('settings.models')); ?>">
                                            <i class="fas fa-cubes"></i>
                                            النماذج الاحتياطية
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </li>
                        <?php endif; ?>

                        <!-- تسجيل الخروج -->
                        <li class="submenu-divider"></li>
                        <li class="submenu-header"><?php echo e(app()->getLocale() === 'ar' ? 'الجلسة' : 'Session'); ?></li>
                        <li class="nav-item">
                            <form action="<?php echo e(route('logout')); ?>" method="POST" id="logout-form" style="display: none;">
                                <?php echo csrf_field(); ?>
                            </form>
                            <a class="nav-link logout-link" href="javascript:void(0)" onclick="confirmLogout()">
                                <i class="fas fa-sign-out-alt"></i>
                                <?php echo e(__('messages.logout')); ?>

                            </a>
                        </li>

                        <!-- معلومات المطور -->
                        <li class="submenu-divider"></li>
                        <li class="nav-item">
                            <div class="developer-info">
                                <div class="developer-card">
                                    <div class="developer-icon">
                                        <i class="fas fa-code"></i>
                                    </div>
                                    <div class="developer-details">
                                        <div class="developer-title">مطور النظام</div>
                                        <div class="developer-name">محمد الاحمدي</div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
                <!-- Topbar -->
                <div class="row topbar mb-4">
                    <div class="col-md-6 d-flex align-items-center">
                        <div class="user-info">
                            <div class="user-avatar">
                                <span><?php echo e(substr(auth()->user()->name, 0, 1)); ?></span>
                            </div>
                            <div>
                                <div><?php echo e(auth()->user()->name); ?></div>
                                <small class="text-muted">
                                    <?php if(auth()->user()->isAdmin()): ?>
                                        <?php echo e(app()->getLocale() === 'ar' ? 'مدير النظام' : 'System Administrator'); ?>

                                    <?php else: ?>
                                        <?php echo e(app()->getLocale() === 'ar' ? 'موظف' : 'Employee'); ?>

                                    <?php endif; ?>
                                    - <?php echo e(__('messages.last_update')); ?>: <?php echo e(now()->format('H:i')); ?>

                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 d-flex align-items-center justify-content-end">
                        <!-- Language Switcher -->
                        <?php if (isset($component)) { $__componentOriginal8d3bff7d7383a45350f7495fc470d934 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8d3bff7d7383a45350f7495fc470d934 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.language-switcher','data' => ['class' => 'me-3']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('language-switcher'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'me-3']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8d3bff7d7383a45350f7495fc470d934)): ?>
<?php $attributes = $__attributesOriginal8d3bff7d7383a45350f7495fc470d934; ?>
<?php unset($__attributesOriginal8d3bff7d7383a45350f7495fc470d934); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8d3bff7d7383a45350f7495fc470d934)): ?>
<?php $component = $__componentOriginal8d3bff7d7383a45350f7495fc470d934; ?>
<?php unset($__componentOriginal8d3bff7d7383a45350f7495fc470d934); ?>
<?php endif; ?>

                        <div class="date-display ms-3">
                            <i class="far fa-calendar-alt ms-1"></i>
                            <?php echo e(now()->format('l, d F Y')); ?>

                        </div>
                    </div>
                </div>

        <!-- Page Content -->
        <?php echo $__env->yieldContent('content'); ?>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Removed Alpine.js - using vanilla JavaScript instead -->
    <!-- Custom JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التحكم في القائمة الجانبية للهواتف
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');

            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                    sidebarOverlay.classList.toggle('show');
                });

                sidebarOverlay.addEventListener('click', function() {
                    sidebar.classList.remove('show');
                    sidebarOverlay.classList.remove('show');
                });
            }

            // تأثير التحميل للبطاقات الإحصائية
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('loaded');
                }, 100 * index);
            });

            // تأثير التحميل للأرقام
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(number => {
                const finalValue = parseInt(number.textContent);
                if (!isNaN(finalValue)) {
                    animateValue(number, 0, finalValue, 1500);
                }
            });

            // التحكم في القوائم المنسدلة - إصدار محسن ومطور
            const dropdownToggles = document.querySelectorAll('.dropdown-toggle');

            // تهيئة حالة القوائم المنسدلة عند تحميل الصفحة
            dropdownToggles.forEach(toggle => {
                const targetId = toggle.getAttribute('data-bs-target');
                const targetElement = document.querySelector(targetId);
                const icon = toggle.querySelector('.dropdown-icon');

                // تحديث حالة الأيقونة بناءً على حالة القائمة
                if (targetElement && targetElement.classList.contains('show')) {
                    toggle.setAttribute('aria-expanded', 'true');
                    toggle.classList.add('active');
                    if (icon) {
                        icon.style.transform = 'rotate(180deg)';
                    }
                } else {
                    toggle.setAttribute('aria-expanded', 'false');
                    toggle.classList.remove('active');
                    if (icon) {
                        icon.style.transform = 'rotate(0deg)';
                    }
                }
            });

            // دالة لإغلاق جميع القوائم المنسدلة
            function closeAllDropdowns() {
                dropdownToggles.forEach(toggle => {
                    const targetId = toggle.getAttribute('data-bs-target');
                    const targetElement = document.querySelector(targetId);
                    const icon = toggle.querySelector('.dropdown-icon');

                    if (targetElement && targetElement.classList.contains('show')) {
                        targetElement.classList.remove('show');
                        toggle.setAttribute('aria-expanded', 'false');
                        toggle.classList.remove('active');
                        if (icon) {
                            icon.style.transform = 'rotate(0deg)';
                        }
                    }
                });
            }

            // دالة لفتح قائمة منسدلة محددة
            function openDropdown(toggle, targetElement, icon) {
                targetElement.classList.add('show');
                toggle.setAttribute('aria-expanded', 'true');
                toggle.classList.add('active');
                if (icon) {
                    icon.style.transform = 'rotate(180deg)';
                }

                // التمرير السلس إلى القائمة المفتوحة
                setTimeout(() => {
                    const targetId = toggle.getAttribute('data-bs-target');
                    const rect = targetElement.getBoundingClientRect();
                    const sidebarRect = sidebar.getBoundingClientRect();

                    // إصلاح خاص لقائمة الإعدادات الطويلة
                    if (targetId === '#settingsSubmenu') {
                        toggle.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    } else if (rect.bottom > sidebarRect.bottom) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'end'
                        });
                    }
                }, 300);
            }

            // إضافة event listeners للقوائم المنسدلة
            dropdownToggles.forEach(toggle => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const targetId = this.getAttribute('data-bs-target');
                    const targetElement = document.querySelector(targetId);
                    const icon = this.querySelector('.dropdown-icon');

                    if (targetElement) {
                        const isCurrentlyOpen = targetElement.classList.contains('show');

                        // إغلاق جميع القوائم المنسدلة أولاً
                        closeAllDropdowns();

                        // إذا لم تكن القائمة مفتوحة، افتحها
                        if (!isCurrentlyOpen) {
                            openDropdown(this, targetElement, icon);
                        }
                    }
                });
            });

            // تحسين التمرير بعجلة الماوس في القائمة الجانبية
            sidebar.addEventListener('wheel', function(e) {
                // السماح بالتمرير الطبيعي
                e.stopPropagation();
            });

            // إضافة معالج خاص للقوائم المنسدلة
            const settingsSubmenu = document.getElementById('settingsSubmenu');
            if (settingsSubmenu) {
                settingsSubmenu.addEventListener('wheel', function(e) {
                    e.stopPropagation();
                });
            }

            // إخفاء badge "جديد" بعد النقر على رابط الإعدادات
            const settingsLink = document.querySelector('a[href*="settings"]');
            if (settingsLink) {
                settingsLink.addEventListener('click', function() {
                    const badge = this.querySelector('.badge');
                    if (badge) {
                        badge.style.animation = 'fadeOut 0.5s ease-out forwards';
                        setTimeout(() => {
                            badge.style.display = 'none';
                            // حفظ في localStorage أن المستخدم زار الصفحة
                            localStorage.setItem('settings_visited', 'true');
                        }, 500);
                    }
                });

                // إخفاء badge إذا كان المستخدم زار الصفحة من قبل
                if (localStorage.getItem('settings_visited') === 'true') {
                    const badge = settingsLink.querySelector('.badge');
                    if (badge) {
                        badge.style.display = 'none';
                    }
                }
            }
        });

        // دالة لتحريك الأرقام
        function animateValue(obj, start, end, duration) {
            let startTimestamp = null;
            const step = (timestamp) => {
                if (!startTimestamp) startTimestamp = timestamp;
                const progress = Math.min((timestamp - startTimestamp) / duration, 1);
                obj.innerHTML = Math.floor(progress * (end - start) + start);
                if (progress < 1) {
                    window.requestAnimationFrame(step);
                }
            };
            window.requestAnimationFrame(step);
        }

        // دالة تأكيد تسجيل الخروج
        function confirmLogout() {
            // إنشاء modal تأكيد مخصص
            const confirmModal = document.createElement('div');
            confirmModal.className = 'logout-confirm-modal';
            confirmModal.innerHTML = `
                <div class="logout-confirm-overlay">
                    <div class="logout-confirm-box">
                        <div class="logout-confirm-header">
                            <i class="fas fa-sign-out-alt"></i>
                            <h4><?php echo e(app()->getLocale() === 'ar' ? 'تأكيد تسجيل الخروج' : 'Confirm Logout'); ?></h4>
                        </div>
                        <div class="logout-confirm-body">
                            <p><?php echo e(app()->getLocale() === 'ar' ? 'هل أنت متأكد من أنك تريد تسجيل الخروج من النظام؟' : 'Are you sure you want to logout from the system?'); ?></p>
                        </div>
                        <div class="logout-confirm-footer">
                            <button class="btn btn-danger" onclick="proceedLogout()">
                                <i class="fas fa-sign-out-alt"></i>
                                <?php echo e(app()->getLocale() === 'ar' ? 'نعم، تسجيل الخروج' : 'Yes, Logout'); ?>

                            </button>
                            <button class="btn btn-secondary" onclick="cancelLogout()">
                                <i class="fas fa-times"></i>
                                <?php echo e(app()->getLocale() === 'ar' ? 'إلغاء' : 'Cancel'); ?>

                            </button>
                        </div>
                    </div>
                </div>
            `;

            // إضافة CSS للـ modal
            const style = document.createElement('style');
            style.textContent = `
                .logout-confirm-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 9999;
                    animation: fadeIn 0.3s ease;
                }
                .logout-confirm-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .logout-confirm-box {
                    background: white;
                    border-radius: 0.5rem;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    max-width: 400px;
                    width: 90%;
                    animation: slideIn 0.3s ease;
                }
                .logout-confirm-header {
                    padding: 1.5rem;
                    border-bottom: 1px solid #dee2e6;
                    text-align: center;
                    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                    color: white;
                    border-radius: 0.5rem 0.5rem 0 0;
                }
                .logout-confirm-header i {
                    font-size: 2rem;
                    margin-bottom: 0.5rem;
                    color: #ffc107;
                }
                .logout-confirm-header h4 {
                    margin: 0;
                    font-weight: 600;
                }
                .logout-confirm-body {
                    padding: 1.5rem;
                    text-align: center;
                    color: #495057;
                }
                .logout-confirm-footer {
                    padding: 1rem 1.5rem;
                    border-top: 1px solid #dee2e6;
                    display: flex;
                    gap: 0.5rem;
                    justify-content: center;
                }
                .logout-confirm-footer .btn {
                    padding: 0.5rem 1rem;
                    border: none;
                    border-radius: 0.25rem;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    font-weight: 500;
                }
                .logout-confirm-footer .btn-danger {
                    background: #dc3545;
                    color: white;
                }
                .logout-confirm-footer .btn-danger:hover {
                    background: #c82333;
                    transform: translateY(-1px);
                }
                .logout-confirm-footer .btn-secondary {
                    background: #6c757d;
                    color: white;
                }
                .logout-confirm-footer .btn-secondary:hover {
                    background: #5a6268;
                    transform: translateY(-1px);
                }
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                @keyframes slideIn {
                    from { transform: translateY(-50px) scale(0.9); opacity: 0; }
                    to { transform: translateY(0) scale(1); opacity: 1; }
                }
            `;

            document.head.appendChild(style);
            document.body.appendChild(confirmModal);
        }

        // دالة المتابعة مع تسجيل الخروج
        function proceedLogout() {
            document.getElementById('logout-form').submit();
        }

        // دالة إلغاء تسجيل الخروج
        function cancelLogout() {
            const modal = document.querySelector('.logout-confirm-modal');
            if (modal) {
                modal.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }
    </script>
    <?php echo $__env->yieldContent('scripts'); ?>
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp2\htdocs\app\resources\views/layouts/admin.blade.php ENDPATH**/ ?>