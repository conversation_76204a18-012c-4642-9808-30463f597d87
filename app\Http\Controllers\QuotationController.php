<?php

namespace App\Http\Controllers;

use App\Models\Quotation;
use App\Models\Branch;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class QuotationController extends Controller
{
    /**
     * Display a listing of quotations.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // التحقق من الصلاحيات
        if (!$user->isAdmin() && !hasPermission('view_quotations')) {
            abort(403, 'غير مصرح لك بعرض عروض الأسعار');
        }

        // بناء الاستعلام الأساسي
        $query = Quotation::with(['branch', 'user', 'creator']);

        // تطبيق الفلترة حسب صلاحيات المستخدم
        if (!$user->isAdmin()) {
            $query->where('user_id', $user->id);
        }

        // تطبيق الفلاتر
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('quotation_number', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_phone', 'like', "%{$search}%")
                  ->orWhere('goods_name', 'like', "%{$search}%");
            });
        }

        // ترتيب النتائج
        $quotations = $query->orderBy('created_at', 'desc')->paginate(15);

        // الحصول على الفروع للفلترة
        $branches = Branch::all();

        // حساب الإحصائيات
        $statistics = Quotation::getUnifiedStatistics($user);

        return view('quotations.index', compact('quotations', 'branches', 'statistics'));
    }

    /**
     * Show the form for creating a new quotation.
     */
    public function create()
    {
        $user = Auth::user();
        
        // التحقق من الصلاحيات
        if (!hasPermission('create_quotations')) {
            abort(403, 'غير مصرح لك بإنشاء عروض أسعار');
        }

        // الحصول على الفروع
        if ($user->isAdmin()) {
            $branches = Branch::all();
        } else {
            $branches = Branch::where('id', $user->branch_id)->get();
        }

        // الحصول على المستخدمين
        if ($user->isAdmin()) {
            $users = User::where('is_active', true)->get();
        } else {
            $users = User::where('id', $user->id)->get();
        }

        return view('quotations.create', compact('branches', 'users'));
    }

    /**
     * Store a newly created quotation in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'customer_email' => 'nullable|email|max:255',
            'customer_address' => 'nullable|string',
            'branch_id' => 'required|exists:branches,id',
            'user_id' => 'required|exists:users,id',
            'service_type' => 'nullable|string|max:255',
            'goods_name' => 'required|string|max:255',
            'goods_type' => 'nullable|string|max:255',
            'country_of_origin' => 'nullable|string|max:255',
            'weight' => 'nullable|numeric|min:0.1',
            'quantity' => 'nullable|string|max:255',
            'departure_area' => 'nullable|string|max:255',
            'delivery_area' => 'nullable|string|max:255',
            'service_fees' => 'required|numeric|min:0',
            'currency' => 'required|string|in:ريال,دولار,ريال سعودي,درهم',
            'delivery_time' => 'nullable|string|max:255',
            'valid_until' => 'required|date|after:today',
            'notes' => 'nullable|string',
            'status' => 'required|string|in:draft,sent',
        ]);

        // إضافة اسم المستخدم
        $user = User::find($validated['user_id']);
        $validated['user_name'] = $user ? $user->name : Auth::user()->name;

        // ربط عرض السعر بالمستخدم الحالي
        $validated['created_by'] = Auth::id();

        Quotation::create($validated);

        return redirect()->route('quotations.index')
            ->with('success', 'تم إنشاء عرض السعر بنجاح');
    }

    /**
     * Display the specified quotation.
     */
    public function show(Quotation $quotation)
    {
        $user = Auth::user();
        
        // التحقق من الصلاحيات
        if (!$user->isAdmin() && $quotation->user_id !== $user->id) {
            abort(403, 'غير مصرح لك بعرض هذا العرض');
        }

        return view('quotations.show', compact('quotation'));
    }

    /**
     * Show the form for editing the specified quotation.
     */
    public function edit(Quotation $quotation)
    {
        $user = Auth::user();
        
        // التحقق من الصلاحيات
        if (!hasPermission('edit_quotations') || (!$user->isAdmin() && $quotation->user_id !== $user->id)) {
            abort(403, 'غير مصرح لك بتعديل هذا العرض');
        }

        // منع التعديل إذا كان العرض مقبول أو محول لطلب
        if (in_array($quotation->status, ['accepted', 'converted'])) {
            return redirect()->route('quotations.show', $quotation)
                ->with('error', 'لا يمكن تعديل عرض السعر بعد قبوله أو تحويله لطلب');
        }

        // الحصول على الفروع
        if ($user->isAdmin()) {
            $branches = Branch::all();
        } else {
            $branches = Branch::where('id', $user->branch_id)->get();
        }

        // الحصول على المستخدمين
        if ($user->isAdmin()) {
            $users = User::where('is_active', true)->get();
        } else {
            $users = User::where('id', $user->id)->get();
        }

        return view('quotations.edit', compact('quotation', 'branches', 'users'));
    }

    /**
     * Update the specified quotation in storage.
     */
    public function update(Request $request, Quotation $quotation)
    {
        $user = Auth::user();
        
        // التحقق من الصلاحيات
        if (!hasPermission('edit_quotations') || (!$user->isAdmin() && $quotation->user_id !== $user->id)) {
            abort(403, 'غير مصرح لك بتعديل هذا العرض');
        }

        // منع التعديل إذا كان العرض مقبول أو محول لطلب
        if (in_array($quotation->status, ['accepted', 'converted'])) {
            return redirect()->route('quotations.show', $quotation)
                ->with('error', 'لا يمكن تعديل عرض السعر بعد قبوله أو تحويله لطلب');
        }

        $validated = $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'customer_email' => 'nullable|email|max:255',
            'customer_address' => 'nullable|string',
            'service_type' => 'nullable|string|max:255',
            'goods_name' => 'required|string|max:255',
            'goods_type' => 'nullable|string|max:255',
            'country_of_origin' => 'nullable|string|max:255',
            'weight' => 'nullable|numeric|min:0',
            'quantity' => 'nullable|string|max:255',
            'branch_id' => 'required|exists:branches,id',
            'departure_area' => 'nullable|string|max:255',
            'delivery_area' => 'nullable|string|max:255',
            'service_fees' => 'required|numeric|min:0',
            'currency' => 'required|string|in:ريال,دولار,ريال سعودي,درهم',
            'delivery_time' => 'nullable|string|max:255',
            'valid_until' => 'required|date|after:today',
            'notes' => 'nullable|string',
            'status' => 'required|string|in:draft,sent,accepted,rejected,expired',
        ]);

        $quotation->update($validated);

        return redirect()->route('quotations.show', $quotation)
            ->with('success', 'تم تحديث عرض السعر بنجاح');
    }

    /**
     * Remove the specified quotation from storage.
     */
    public function destroy(Quotation $quotation)
    {
        $user = Auth::user();
        
        // التحقق من الصلاحيات
        if (!hasPermission('delete_quotations') || (!$user->isAdmin() && $quotation->user_id !== $user->id)) {
            abort(403, 'غير مصرح لك بحذف هذا العرض');
        }

        // منع الحذف إذا كان العرض مقبول أو محول لطلب
        if (in_array($quotation->status, ['accepted', 'converted'])) {
            return redirect()->route('quotations.index')
                ->with('error', 'لا يمكن حذف عرض السعر بعد قبوله أو تحويله لطلب');
        }

        $quotation->delete();

        return redirect()->route('quotations.index')
            ->with('success', 'تم حذف عرض السعر بنجاح');
    }

    /**
     * Update quotation status
     */
    public function updateStatus(Request $request, Quotation $quotation)
    {
        $user = Auth::user();
        
        // التحقق من الصلاحيات
        if (!$user->isAdmin() && $quotation->user_id !== $user->id) {
            abort(403, 'غير مصرح لك بتحديث حالة هذا العرض');
        }

        $validated = $request->validate([
            'status' => 'required|string|in:draft,sent,accepted,rejected,expired,converted'
        ]);

        $quotation->update(['status' => $validated['status']]);

        return redirect()->back()
            ->with('success', 'تم تحديث حالة عرض السعر بنجاح');
    }

    /**
     * Convert quotation to order
     */
    public function convertToOrder(Quotation $quotation)
    {
        $user = Auth::user();
        
        // التحقق من الصلاحيات
        if (!hasPermission('create_orders') || (!$user->isAdmin() && $quotation->user_id !== $user->id)) {
            abort(403, 'غير مصرح لك بتحويل هذا العرض لطلب');
        }

        // التحقق من حالة العرض
        if ($quotation->status !== 'accepted') {
            return redirect()->back()
                ->with('error', 'يجب أن يكون عرض السعر مقبولاً لتحويله لطلب');
        }

        try {
            DB::beginTransaction();

            // إنشاء طلب جديد من بيانات عرض السعر
            $orderData = [
                'customer_name' => $quotation->customer_name,
                'phone_number' => $quotation->customer_phone,
                'branch_id' => $quotation->branch_id,
                'user_id' => $quotation->user_id,
                'service_type' => $quotation->service_type,
                'goods_name' => $quotation->goods_name,
                'goods_type' => $quotation->goods_type,
                'country_of_origin' => $quotation->country_of_origin,
                'weight' => $quotation->weight,
                'quantity' => $quotation->quantity,
                'departure_area' => $quotation->departure_area,
                'delivery_area' => $quotation->delivery_area,
                'service_fees' => $quotation->service_fees,
                'currency' => $quotation->currency,
                'delivery_time' => $quotation->delivery_time,
                'notes' => $quotation->notes,
                'amount' => $quotation->service_fees,
                'status' => 'processing',
                'user_name' => $quotation->user_name,
                'created_by' => Auth::id(),
                'request_date' => now(),
            ];

            $order = \App\Models\Order::create($orderData);

            // تحديث حالة عرض السعر
            $quotation->update(['status' => 'converted']);

            DB::commit();

            return redirect()->route('orders.show', $order)
                ->with('success', 'تم تحويل عرض السعر إلى طلب بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء تحويل عرض السعر: ' . $e->getMessage());
        }
    }

    /**
     * API للحصول على الرقم التسلسلي التالي للفرع
     */
    public function getNextSerial($branchCode)
    {
        try {
            $nextSerial = Quotation::getNextSerialForBranch($branchCode);
            $year = date('y');
            $quotationNumber = 'QT-' . $branchCode . $year . '-' . str_pad($nextSerial, 2, '0', STR_PAD_LEFT);

            return response()->json([
                'success' => true,
                'next_serial' => $nextSerial,
                'quotation_number' => $quotationNumber,
                'branch_code' => $branchCode,
                'year' => $year
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في الحصول على الرقم التسلسلي: ' . $e->getMessage()
            ], 500);
        }
    }
}
