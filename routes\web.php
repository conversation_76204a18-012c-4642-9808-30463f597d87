<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\DatabaseExampleController;
use App\Http\Controllers\AuthExampleController;
use App\Http\Controllers\GuardExampleController;
use App\Http\Controllers\LogoutController;
use App\Http\Controllers\PasswordHashingController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\LanguageController;

Route::get('/', function () {
    return redirect()->route('login');
});

// Language switching routes
Route::get('/language/{locale}', [LanguageController::class, 'switch'])->name('language.switch');
Route::get('/api/language/current', [LanguageController::class, 'current'])->name('language.current');
Route::get('/api/language/translations/{locale?}', [LanguageController::class, 'translations'])->name('language.translations');

// Language test page
Route::get('/language-test', function () {
    return view('language-test');
})->name('language.test')->middleware('auth');

// Language debug page
Route::get('/language-debug', function () {
    return view('language-debug');
})->name('language.debug')->middleware('auth');

// Rutas protegidas por autenticación
Route::middleware(['auth'])->group(function () {
    // Dashboard routes
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Order routes (moved to protected section below)

    // Basic routes (will be overridden by protected routes below)

    // Settings routes
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::post('/settings', [SettingsController::class, 'update'])->name('settings.update');

    Route::get('/settings/system', [SettingsController::class, 'system'])->name('settings.system');
    Route::get('/settings/backup', [SettingsController::class, 'backup'])->name('settings.backup');
    Route::get('/settings/activity', [SettingsController::class, 'activity'])->name('settings.activity');
    Route::get('/settings/models', [SettingsController::class, 'models'])->name('settings.models');
    Route::get('/api/settings/news-ticker', [SettingsController::class, 'getNewsTickerSettings'])->name('api.settings.news-ticker');
    Route::post('/api/settings/news-ticker', [SettingsController::class, 'updateNewsTickerSettingsApi'])->name('api.settings.news-ticker.update');
    Route::post('/settings/clear-cache', [SettingsController::class, 'clearCache'])->name('settings.clear-cache');

    // مسارات النظام الجديد للشريط الإخباري
    Route::prefix('admin')->name('admin.')->group(function () {
        Route::get('/news-ticker', [\App\Http\Controllers\Admin\NewsTickerController::class, 'index'])->name('news-ticker.index');
        Route::put('/news-ticker', [\App\Http\Controllers\Admin\NewsTickerController::class, 'update'])->name('news-ticker.update');
        Route::post('/news-ticker/toggle', [\App\Http\Controllers\Admin\NewsTickerController::class, 'toggle'])->name('news-ticker.toggle');
        Route::post('/news-ticker/clear-cache', [\App\Http\Controllers\Admin\NewsTickerController::class, 'clearCache'])->name('news-ticker.clear-cache');
        Route::post('/news-ticker/refresh-cache', [\App\Http\Controllers\Admin\NewsTickerController::class, 'refreshCache'])->name('news-ticker.refresh-cache');
    });

    // API للشريط الإخباري الجديد
    Route::get('/api/news-ticker', [\App\Http\Controllers\TempNewsTickerController::class, 'getApiData'])->name('api.news-ticker');

    // API مؤقت للإعدادات القديمة
    Route::get('/api/settings/news-ticker', [\App\Http\Controllers\TempNewsTickerController::class, 'getSettingsData'])->name('api.settings.news-ticker.temp');



    // Database Testing Routes for SQL Server
    Route::prefix('database-test')->name('db.test.')->group(function () {
        Route::get('/select', [DatabaseExampleController::class, 'selectExample'])->name('select');
        Route::get('/scalar', [DatabaseExampleController::class, 'scalarExample'])->name('scalar');
        Route::post('/insert', [DatabaseExampleController::class, 'insertExample'])->name('insert');
        Route::put('/update/{id}', [DatabaseExampleController::class, 'updateExample'])->name('update');
        Route::delete('/delete/{id}', [DatabaseExampleController::class, 'deleteExample'])->name('delete');
        Route::post('/transaction', [DatabaseExampleController::class, 'transactionExample'])->name('transaction');
        Route::get('/query-builder', [DatabaseExampleController::class, 'queryBuilderExample'])->name('query-builder');
        Route::get('/read-write', [DatabaseExampleController::class, 'readWriteExample'])->name('read-write');
        Route::get('/arabic-support', [DatabaseExampleController::class, 'testArabicSupport'])->name('arabic-support');
        Route::get('/sql-security', [DatabaseExampleController::class, 'sqlSecurityExample'])->name('sql-security');
        Route::get('/named-bindings', [DatabaseExampleController::class, 'namedBindingsExample'])->name('named-bindings');
        Route::get('/stored-procedures', [DatabaseExampleController::class, 'storedProcedureExample'])->name('stored-procedures');
        Route::get('/update-advanced', [DatabaseExampleController::class, 'updateExampleAdvanced'])->name('update-advanced');
        Route::get('/delete-advanced', [DatabaseExampleController::class, 'deleteExampleAdvanced'])->name('delete-advanced');
    });

    // Authentication Testing Routes
    Route::prefix('auth-test')->name('auth.test.')->group(function () {
        Route::get('/basic', [AuthExampleController::class, 'basicAuthExamples'])->name('basic');
        Route::get('/user-operations', [AuthExampleController::class, 'userBasedOperations'])->name('user-operations');
        Route::post('/create-order', [AuthExampleController::class, 'createOrderForAuthUser'])->name('create-order');
        Route::put('/update-order/{orderId}', [AuthExampleController::class, 'updateUserOrder'])->name('update-order');
        Route::get('/dashboard', [AuthExampleController::class, 'getUserDashboard'])->name('dashboard');
        Route::post('/log-activity/{action}', [AuthExampleController::class, 'logUserActivity'])->name('log-activity');
        Route::get('/methods-comparison', [AuthExampleController::class, 'authMethodsComparison'])->name('methods-comparison');
        Route::put('/update-order-request/{orderId}', [AuthExampleController::class, 'updateOrderWithRequestUser'])->name('update-order-request');
        Route::post('/store-order-request', [AuthExampleController::class, 'storeOrderWithRequestUser'])->name('store-order-request');
        Route::get('/multi-guard', [AuthExampleController::class, 'multiGuardExample'])->name('multi-guard');
    });

    // Guard Examples Routes
    Route::prefix('guard-examples')->name('guard.examples.')->group(function () {
        Route::get('/web-guard', [GuardExampleController::class, 'webGuardExample'])->name('web-guard');
        Route::get('/api-guard', [GuardExampleController::class, 'apiGuardExample'])->name('api-guard');
        Route::get('/multiple-guards', [GuardExampleController::class, 'multipleGuardsExample'])->name('multiple-guards');
        Route::post('/login-specific-guard', [GuardExampleController::class, 'loginWithSpecificGuard'])->name('login-specific-guard');
        Route::post('/logout-specific-guard', [GuardExampleController::class, 'logoutFromSpecificGuard'])->name('logout-specific-guard');
        Route::get('/smart-redirect', [GuardExampleController::class, 'smartRedirectExample'])->name('smart-redirect');
    });

    // Admin Guard Protected Routes
    Route::middleware(['auth:admin'])->prefix('admin-guard-examples')->name('admin.guard.examples.')->group(function () {
        Route::get('/dashboard', [GuardExampleController::class, 'adminGuardExample'])->name('dashboard');
        Route::get('/middleware-specific', [GuardExampleController::class, 'guardSpecificMiddleware'])->name('middleware-specific');
    });

    // Password Hashing Management Routes
    Route::prefix('password-management')->name('password.')->group(function () {
        Route::get('/info', [PasswordHashingController::class, 'showHashingInfo'])->name('info');
        Route::put('/update', [PasswordHashingController::class, 'updatePassword'])->name('update');
        Route::post('/force-rehash', [PasswordHashingController::class, 'forceRehash'])->name('force-rehash');
        Route::get('/system-stats', [PasswordHashingController::class, 'getSystemHashingStats'])->name('system-stats');
    });

    // Protected routes with auto password rehashing
    Route::middleware(['auth', 'auto.rehash'])->group(function () {
        Route::get('/secure-with-rehash', function () {
            $rehashInfo = [];

            if (session('password_rehashed')) {
                $rehashInfo = session('rehash_info', []);
            }

            return response()->json([
                'message' => 'مسار آمن مع إعادة تجزئة تلقائية',
                'user' => Auth::user() ? Auth::user()->name : 'غير محدد',
                'password_rehashed' => session('password_rehashed', false),
                'rehash_info' => $rehashInfo,
                'password_expired' => session('password_expired', false),
                'password_expiring_soon' => session('password_expiring_soon', false),
                'days_remaining' => session('days_remaining'),
                'security_status' => 'محسن'
            ]);
        })->name('secure.with.rehash');
    });

    // News Ticker Instant Update Routes (خبرة 80 سنة)
    Route::prefix('admin/news-ticker')->name('admin.news-ticker.')->middleware(['auth'])->group(function () {
        Route::get('/settings', function () {
            $ticker = \App\Models\NewsTicker::getActive();
            return view('admin.news-ticker-settings', compact('ticker'));
        })->name('settings');

        Route::put('/update', [App\Http\Controllers\Admin\NewsTickerController::class, 'update'])->name('admin-update');

        Route::post('/instant-update', function () {
            try {
                // مسح الكاش فوراً
                Cache::forget('active_news_ticker');
                Cache::forget('news_ticker');

                $ticker = \App\Models\NewsTicker::getActive();

                // إطلاق حدث التحديث الفوري
                event(new \App\Events\NewsTickerUpdated($ticker));

                return response()->json([
                    'success' => true,
                    'message' => 'تم التحديث الفوري بنجاح',
                    'data' => $ticker->toApiFormat(),
                    'timestamp' => now()
                ]);

            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'error' => 'فشل في التحديث الفوري',
                    'message' => $e->getMessage()
                ], 500);
            }
        })->name('instant-update');

        Route::post('/force-refresh', function () {
            try {
                // مسح جميع أنواع الكاش
                $cacheKeys = [
                    'active_news_ticker',
                    'news_ticker',
                    'news_ticker_data',
                    'news_ticker_api_settings'
                ];

                foreach ($cacheKeys as $key) {
                    Cache::forget($key);
                }

                $ticker = \App\Models\NewsTicker::getActive();

                return response()->json([
                    'success' => true,
                    'message' => 'تم تحديث الكاش بقوة',
                    'data' => $ticker->toApiFormat(),
                    'cache_cleared' => true,
                    'timestamp' => now()
                ]);

            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'error' => 'فشل في تحديث الكاش',
                    'message' => $e->getMessage()
                ], 500);
            }
        })->name('force-refresh');
    });

    // صفحة إعدادات بسيطة
    Route::get('/simple-news-settings', function () {
        return view('simple-news-settings');
    })->name('simple.news.settings');

    // مسارات الشريط الإخباري المحسنة
    Route::prefix('news-ticker')->name('news-ticker.')->group(function () {
        Route::get('/settings', [App\Http\Controllers\NewsTickerController::class, 'index'])->name('settings');
        Route::post('/update', [App\Http\Controllers\NewsTickerController::class, 'update'])->name('update');
        Route::get('/data', [App\Http\Controllers\NewsTickerController::class, 'getData'])->name('data');
        Route::get('/test-db', [App\Http\Controllers\NewsTickerController::class, 'testDatabase'])->name('test-db');
    });

    // مسارات إدارة الصلاحيات
    Route::prefix('permissions')->name('permissions.')->middleware(['user.permission:manage_permissions'])->group(function () {
        Route::get('/', [App\Http\Controllers\PermissionController::class, 'index'])->name('index');
        Route::get('/users', [App\Http\Controllers\PermissionController::class, 'getUsers'])->name('users');
        Route::get('/roles', [App\Http\Controllers\PermissionController::class, 'getRoles'])->name('roles');
        Route::get('/permissions', [App\Http\Controllers\PermissionController::class, 'getPermissions'])->name('permissions');
        Route::get('/stats', [App\Http\Controllers\PermissionController::class, 'getStats'])->name('stats');

        // مسارات المستخدمين
        Route::post('/users', [App\Http\Controllers\PermissionController::class, 'createUser'])->name('users.create');
        Route::get('/users/{user}', [App\Http\Controllers\PermissionController::class, 'getUser'])->name('users.show');
        Route::put('/users/{user}', [App\Http\Controllers\PermissionController::class, 'updateUser'])->name('users.update');
        Route::delete('/users/{user}', [App\Http\Controllers\PermissionController::class, 'deleteUser'])->name('users.delete');
        Route::patch('/users/{user}/toggle', [App\Http\Controllers\PermissionController::class, 'toggleUserStatus'])->name('users.toggle');

        // مسارات صلاحيات المستخدمين
        Route::get('/users/{user}/permissions', [App\Http\Controllers\PermissionController::class, 'getUserPermissions'])->name('users.permissions.show');
        Route::put('/users/{user}/permissions', [App\Http\Controllers\PermissionController::class, 'updateUserPermissionsNew'])->name('users.permissions.update');

        // مسارات الأدوار
        Route::post('/roles', [App\Http\Controllers\PermissionController::class, 'createRole'])->name('roles.create');
        Route::get('/roles/{role}', [App\Http\Controllers\PermissionController::class, 'getRole'])->name('roles.show');
        Route::put('/roles/{role}', [App\Http\Controllers\PermissionController::class, 'updateRole'])->name('roles.update');
        Route::delete('/roles/{role}', [App\Http\Controllers\PermissionController::class, 'deleteRole'])->name('roles.delete');
        Route::patch('/roles/{role}/toggle', [App\Http\Controllers\PermissionController::class, 'toggleRoleStatus'])->name('roles.toggle');

        // مسارات صلاحيات الأدوار
        Route::get('/roles/{role}/permissions', [App\Http\Controllers\PermissionController::class, 'getRolePermissions'])->name('roles.permissions.show');
        Route::put('/roles/{role}/permissions', [App\Http\Controllers\PermissionController::class, 'updateRolePermissions'])->name('roles.permissions.update');

        // مسارات الصلاحيات
        Route::post('/permissions', [App\Http\Controllers\PermissionController::class, 'createPermission'])->name('permissions.create');

        // مسار الفروع للاستخدام في إنشاء المستخدمين
        Route::get('/branches', [App\Http\Controllers\PermissionController::class, 'getBranches'])->name('branches');
    });

    // مسارات الطلبات - محمية بنظام الصلاحيات الجديد
    Route::prefix('orders')->name('orders.')->group(function () {
        Route::get('/', [App\Http\Controllers\OrderController::class, 'index'])->name('index')->middleware('user.permission:view_orders');
        Route::get('/create', [App\Http\Controllers\OrderController::class, 'create'])->name('create')->middleware('user.permission:create_orders');
        Route::post('/', [App\Http\Controllers\OrderController::class, 'store'])->name('store')->middleware('user.permission:create_orders');
        Route::get('/{order}', [App\Http\Controllers\OrderController::class, 'show'])->name('show')->middleware('user.permission:view_orders');
        Route::get('/{order}/edit', [App\Http\Controllers\OrderController::class, 'edit'])->name('edit')->middleware('user.permission:edit_orders');
        Route::put('/{order}', [App\Http\Controllers\OrderController::class, 'update'])->name('update')->middleware('user.permission:edit_orders');
        Route::delete('/{order}', [App\Http\Controllers\OrderController::class, 'destroy'])->name('destroy')->middleware('user.permission:delete_orders');

        // مسارات الطباعة مع شعار XCMYWO
        Route::get('/{order}/print-pdf', [App\Http\Controllers\OrderController::class, 'printNewTemplate'])->name('print.pdf');
        Route::get('/{order}/print', [App\Http\Controllers\OrderController::class, 'printHtmlTemplate'])->name('print');


    });

    // API routes للحصول على الرقم التسلسلي التالي
    Route::prefix('api/orders')->group(function () {
        Route::get('/next-serial/{branchCode}', [App\Http\Controllers\OrderController::class, 'getNextSerial'])->middleware('user.permission:view_orders');
    });

    // مسارات عروض الأسعار - محمية بنظام الصلاحيات
    Route::prefix('quotations')->name('quotations.')->group(function () {
        Route::get('/', [App\Http\Controllers\QuotationController::class, 'index'])->name('index')->middleware('user.permission:view_quotations');
        Route::get('/create', [App\Http\Controllers\QuotationController::class, 'create'])->name('create')->middleware('user.permission:create_quotations');
        Route::post('/', [App\Http\Controllers\QuotationController::class, 'store'])->name('store')->middleware('user.permission:create_quotations');
        Route::get('/{quotation}', [App\Http\Controllers\QuotationController::class, 'show'])->name('show')->middleware('user.permission:view_quotations');
        Route::get('/{quotation}/edit', [App\Http\Controllers\QuotationController::class, 'edit'])->name('edit')->middleware('user.permission:edit_quotations');
        Route::put('/{quotation}', [App\Http\Controllers\QuotationController::class, 'update'])->name('update')->middleware('user.permission:edit_quotations');
        Route::delete('/{quotation}', [App\Http\Controllers\QuotationController::class, 'destroy'])->name('destroy')->middleware('user.permission:delete_quotations');

        // مسارات إضافية لعروض الأسعار
        Route::patch('/{quotation}/status', [App\Http\Controllers\QuotationController::class, 'updateStatus'])->name('update-status')->middleware('user.permission:edit_quotations');
        Route::post('/{quotation}/convert-to-order', [App\Http\Controllers\QuotationController::class, 'convertToOrder'])->name('convert-to-order')->middleware('user.permission:create_orders');
    });

    // API routes للحصول على الرقم التسلسلي التالي لعروض الأسعار
    Route::prefix('api/quotations')->group(function () {
        Route::get('/next-serial/{branchCode}', [App\Http\Controllers\QuotationController::class, 'getNextSerial'])->middleware('user.permission:view_quotations');
    });

    // مسارات التقارير - محمية بنظام الصلاحيات الجديد
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [App\Http\Controllers\ReportController::class, 'index'])->name('index')->middleware('user.permission:view_reports');
        Route::get('/weekly', [App\Http\Controllers\ReportController::class, 'weekly'])->name('weekly')->middleware('user.permission:view_reports');
        Route::get('/monthly', [App\Http\Controllers\ReportController::class, 'monthly'])->name('monthly')->middleware('user.permission:view_reports');
        Route::get('/yearly', [App\Http\Controllers\ReportController::class, 'yearly'])->name('yearly')->middleware('user.permission:view_reports');

        // تحميل التقارير
        Route::get('/weekly/pdf', [App\Http\Controllers\ReportController::class, 'weeklyPDF'])->name('weekly.pdf')->middleware('user.permission:export_reports');
        Route::get('/monthly/pdf', [App\Http\Controllers\ReportController::class, 'monthlyPDF'])->name('monthly.pdf')->middleware('user.permission:export_reports');

        // طباعة التقارير مباشرة
        Route::get('/weekly/print', [App\Http\Controllers\ReportController::class, 'weeklyPrint'])->name('weekly.print')->middleware('user.permission:export_reports');
        Route::get('/monthly/print', [App\Http\Controllers\ReportController::class, 'monthlyPrint'])->name('monthly.print')->middleware('user.permission:export_reports');
        Route::get('/yearly/print', [App\Http\Controllers\ReportController::class, 'yearlyPrint'])->name('yearly.print')->middleware('user.permission:export_reports');
    });

    // مسارات مخصصة للمديرين فقط
    Route::group([], function () {
        // إدارة الفروع
        Route::resource('branches', App\Http\Controllers\BranchController::class)->middleware('user.permission:manage_branches');

        // إدارة المستخدمين
        Route::resource('users', App\Http\Controllers\UserController::class)->middleware('user.permission:manage_users');
    });

    // مسارات الفواتير
    Route::get('/invoices', [InvoiceController::class, 'index'])->name('invoices.index');
    Route::get('/invoices/create', [InvoiceController::class, 'create'])->name('invoices.create');
    Route::post('/invoices', [InvoiceController::class, 'store'])->name('invoices.store');
    Route::get('/invoices/{invoice}', [InvoiceController::class, 'show'])->name('invoices.show');
    Route::get('/invoices/{invoice}/edit', [InvoiceController::class, 'edit'])->name('invoices.edit');
    Route::put('/invoices/{invoice}', [InvoiceController::class, 'update'])->name('invoices.update');
    Route::delete('/invoices/{invoice}', [InvoiceController::class, 'destroy'])->name('invoices.destroy');
});

// Auth routes - استخدام elite-login كصفحة تسجيل الدخول الوحيدة
Route::get('/login', function () {
    return view('auth.elite-login');
})->name('login');
Route::get('/elite-login', function () {
    return view('auth.elite-login');
})->name('elite.login');
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Test route for system check
Route::get('/system-check', function() {
    $results = [];

    // Check users
    $adminUser = \App\Models\User::where('email', '<EMAIL>')->first();
    $managerUser = \App\Models\User::where('email', '<EMAIL>')->first();
    $employeeUser = \App\Models\User::where('email', '<EMAIL>')->first();

    $results['users'] = [
        'admin_exists' => $adminUser ? true : false,
        'admin_is_admin' => $adminUser ? $adminUser->isAdmin() : false,
        'admin_roles_count' => $adminUser ? $adminUser->roles->count() : 0,
        'manager_exists' => $managerUser ? true : false,
        'employee_exists' => $employeeUser ? true : false,
        'total_users' => \App\Models\User::count()
    ];

    // Check roles and permissions
    $results['roles'] = [
        'total_roles' => \App\Models\Role::count(),
        'total_permissions' => \App\Models\Permission::count(),
        'admin_permissions' => $adminUser ? $adminUser->getAllPermissions()->count() : 0
    ];

    // Check orders
    $results['orders'] = [
        'total_orders' => \App\Models\Order::count(),
        'admin_can_access_all' => $adminUser ? $adminUser->getAccessibleOrders()->count() : 0
    ];

    // Check branches
    $results['branches'] = [
        'total_branches' => \App\Models\Branch::count()
    ];

    return response()->json($results, 200, [], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
})->name('system.check');

// Test login route
Route::get('/test-login/{email}', function($email) {
    $user = \App\Models\User::where('email', $email)->first();

    if (!$user) {
        return response()->json(['error' => 'المستخدم غير موجود'], 404);
    }

    // Login the user
    \Illuminate\Support\Facades\Auth::login($user);

    return response()->json([
        'success' => true,
        'message' => 'تم تسجيل الدخول بنجاح',
        'user' => [
            'name' => $user->name,
            'email' => $user->email,
            'is_admin' => $user->isAdmin(),
            'roles' => $user->roles->pluck('display_name'),
            'permissions_count' => $user->getAllPermissions()->count()
        ],
        'redirect_url' => route('dashboard')
    ], 200, [], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
})->name('test.login');

// URL Examples route (for development/testing)
Route::get('/url-examples', function () {
    return view('examples.url-examples');
})->name('url.examples')->middleware('auth');

// Logout routes (متقدمة)
Route::middleware(['auth'])->group(function () {
    Route::post('/logout-advanced', [LogoutController::class, 'logout'])->name('logout.advanced');
    Route::post('/logout-guard/{guard}', [LogoutController::class, 'logoutFromGuard'])->name('logout.guard');
    Route::post('/logout-other-devices', [LogoutController::class, 'logoutOtherDevices'])->name('logout.other.devices');
    Route::post('/logout-all-guards', [LogoutController::class, 'logoutAllGuards'])->name('logout.all.guards');
    Route::post('/logout-with-cleanup', [LogoutController::class, 'logoutWithCleanup'])->name('logout.cleanup');
    Route::post('/logout-custom-redirect', [LogoutController::class, 'logoutWithCustomRedirect'])->name('logout.custom.redirect');
});

// API Logout
Route::middleware(['auth:api'])->group(function () {
    Route::post('/api/logout', [LogoutController::class, 'apiLogout'])->name('api.logout');
});

// Protected routes with session authentication
Route::middleware(['auth', 'auth.session'])->group(function () {
    Route::get('/secure-dashboard', function () {
        return response()->json([
            'message' => 'لوحة تحكم آمنة مع مصادقة الجلسة',
            'user' => Auth::user() ? Auth::user()->name : 'غير محدد',
            'session_id' => session()->getId(),
            'last_activity' => session('last_activity')
        ]);
    })->name('secure.dashboard');

    Route::get('/secure-profile', function () {
        return response()->json([
            'message' => 'ملف شخصي آمن',
            'user' => Auth::user() ? Auth::user()->name : null,
            'session_security' => 'مفعل'
        ]);
    })->name('secure.profile');
});

