<?php

use App\Helpers\PermissionHelper;

if (!function_exists('hasPermission')) {
    /**
     * التحقق من صلاحية المستخدم
     */
    function hasPermission($permission)
    {
        return PermissionHelper::hasPermission($permission);
    }
}

if (!function_exists('hasAllPermissions')) {
    /**
     * التحقق من عدة صلاحيات (يجب أن تكون جميعها متوفرة)
     */
    function hasAllPermissions(array $permissions)
    {
        return PermissionHelper::hasAllPermissions($permissions);
    }
}

if (!function_exists('hasAnyPermission')) {
    /**
     * التحقق من عدة صلاحيات (يكفي وجود واحدة منها)
     */
    function hasAnyPermission(array $permissions)
    {
        return PermissionHelper::hasAnyPermission($permissions);
    }
}

if (!function_exists('hasRole')) {
    /**
     * التحقق من الدور
     */
    function hasRole($role)
    {
        return PermissionHelper::hasRole($role);
    }
}

if (!function_exists('hasAnyRole')) {
    /**
     * التحقق من عدة أدوار
     */
    function hasAnyRole(array $roles)
    {
        return PermissionHelper::hasAnyRole($roles);
    }
}

if (!function_exists('getUserPermissions')) {
    /**
     * الحصول على جميع صلاحيات المستخدم
     */
    function getUserPermissions()
    {
        return PermissionHelper::getUserPermissions();
    }
}

if (!function_exists('canAccess')) {
    /**
     * التحقق من إمكانية الوصول لصفحة أو وظيفة
     */
    function canAccess($permission)
    {
        return hasPermission($permission);
    }
}

if (!function_exists('isAdmin')) {
    /**
     * التحقق من كون المستخدم مدير
     */
    function isAdmin()
    {
        return hasRole('admin');
    }
}

if (!function_exists('isManager')) {
    /**
     * التحقق من كون المستخدم مدير قسم
     */
    function isManager()
    {
        return hasRole('manager');
    }
}

if (!function_exists('isEmployee')) {
    /**
     * التحقق من كون المستخدم موظف
     */
    function isEmployee()
    {
        return hasRole('employee');
    }
}

if (!function_exists('canManageUsers')) {
    /**
     * التحقق من إمكانية إدارة المستخدمين
     */
    function canManageUsers()
    {
        return hasAnyPermission(['view_users', 'create_users', 'edit_users', 'delete_users']);
    }
}

if (!function_exists('canManageOrders')) {
    /**
     * التحقق من إمكانية إدارة الطلبات
     */
    function canManageOrders()
    {
        return hasAnyPermission(['view_orders', 'create_orders', 'edit_orders', 'delete_orders']);
    }
}

if (!function_exists('canViewReports')) {
    /**
     * التحقق من إمكانية عرض التقارير
     */
    function canViewReports()
    {
        return hasPermission('view_reports');
    }
}

if (!function_exists('canManageSystem')) {
    /**
     * التحقق من إمكانية إدارة النظام
     */
    function canManageSystem()
    {
        return hasAnyPermission(['system_settings', 'backup_restore', 'manage_permissions']);
    }
}

if (!function_exists('canManageBranches')) {
    /**
     * التحقق من إمكانية إدارة الفروع
     */
    function canManageBranches()
    {
        return hasAnyPermission(['view_branches', 'create_branches', 'edit_branches', 'manage_branches']);
    }
}

if (!function_exists('canManageQuotations')) {
    /**
     * التحقق من إمكانية إدارة عروض الأسعار
     */
    function canManageQuotations()
    {
        return hasAnyPermission(['view_quotations', 'create_quotations', 'edit_quotations', 'delete_quotations']);
    }
}
