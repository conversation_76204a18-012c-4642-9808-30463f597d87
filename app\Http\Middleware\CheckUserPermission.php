<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class CheckUserPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $permission): Response
    {
        // التحقق من تسجيل الدخول
        if (!Auth::check()) {
            if ($request->expectsJson()) {
                return response()->json(['error' => 'غير مصرح لك بالوصول'], 401);
            }
            return redirect()->route('login')->with('error', 'يجب تسجيل الدخول أولاً');
        }

        $user = Auth::user();

        // المدير الرئيسي له جميع الصلاحيات
        if ($user->id == 1 || $user->role === 'admin' || $user->user_type === 'admin') {
            return $next($request);
        }

        // التحقق من وجود الصلاحية للمستخدم
        if ($this->hasPermission($user->id, $permission)) {
            return $next($request);
        }

        // التحقق من صلاحيات الدور
        if ($this->hasRolePermission($user, $permission)) {
            return $next($request);
        }

        // إذا لم يكن لديه الصلاحية
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'ليس لديك صلاحية للوصول لهذه الصفحة',
                'required_permission' => $permission
            ], 403);
        }

        return redirect()->back()->with('error', 'ليس لديك صلاحية للوصول لهذه الصفحة');
    }

    /**
     * التحقق من صلاحية المستخدم المباشرة
     */
    private function hasPermission($userId, $permission)
    {
        try {
            return DB::table('user_permissions')
                ->where('user_id', $userId)
                ->where('permission_name', $permission)
                ->exists();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من صلاحيات الدور
     */
    private function hasRolePermission($user, $permission)
    {
        try {
            // الحصول على أدوار المستخدم
            $userRoles = DB::table('user_roles')
                ->where('user_id', $user->id)
                ->pluck('role_id');

            if ($userRoles->isEmpty()) {
                // إذا لم يكن له أدوار محددة، استخدم الدور الافتراضي
                return $this->hasDefaultRolePermission($user->role ?? $user->user_type, $permission);
            }

            // التحقق من صلاحيات الأدوار
            return DB::table('role_permissions')
                ->whereIn('role_id', $userRoles)
                ->where('permission_name', $permission)
                ->exists();
        } catch (\Exception $e) {
            return $this->hasDefaultRolePermission($user->role ?? $user->user_type, $permission);
        }
    }

    /**
     * التحقق من الصلاحيات الافتراضية حسب الدور
     */
    private function hasDefaultRolePermission($role, $permission)
    {
        $defaultPermissions = [
            'admin' => [
                'create_orders', 'view_orders', 'edit_orders', 'delete_orders',
                'create_quotations', 'view_quotations', 'edit_quotations', 'delete_quotations', 'quotations_status', 'convert_quotations',
                'view_reports', 'export_reports',
                'view_users', 'create_users', 'edit_users', 'delete_users',
                'manage_permissions', 'system_settings', 'backup_restore',
                'manage_branches', 'view_branches', 'create_branches', 'edit_branches'
            ],
            'manager' => [
                'create_orders', 'view_orders', 'edit_orders',
                'create_quotations', 'view_quotations', 'edit_quotations', 'quotations_status', 'convert_quotations',
                'view_reports', 'export_reports',
                'view_users', 'create_users', 'edit_users',
                'view_branches'
            ],
            'employee' => [
                'create_orders', 'view_orders', 'view_own_orders',
                'create_quotations', 'view_quotations', 'edit_quotations'
            ]
        ];

        return in_array($permission, $defaultPermissions[$role] ?? []);
    }
}
