<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\Models\Branch;
use App\Models\User;
use Carbon\Carbon;

class Quotation extends Model
{
    use HasFactory;

    protected $fillable = [
        'quotation_number',
        'customer_name',
        'customer_phone',
        'customer_email',
        'customer_address',
        'service_type',
        'goods_name',
        'goods_type',
        'country_of_origin',
        'weight',
        'quantity',
        'branch_id',
        'departure_area',
        'delivery_area',
        'service_fees',
        'currency',
        'delivery_time',
        'valid_until',
        'notes',
        'status',
        'user_id',
        'user_name',
        'created_by',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'valid_until' => 'date',
        'service_fees' => 'decimal:2',
        'weight' => 'decimal:2',
    ];

    /**
     * Boot method لتوليد رقم عرض السعر تلقائياً
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($quotation) {
            if (!$quotation->quotation_number && $quotation->branch_id) {
                $branch = \App\Models\Branch::find($quotation->branch_id);
                if ($branch && $branch->code) {
                    $quotation->quotation_number = self::generateQuotationNumber($branch->code);
                }
            }
        });
    }

    // العلاقة مع المستخدم
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // العلاقة مع الفرع
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    // العلاقة مع المستخدم الذي أنشأ عرض السعر
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // دالة مساعدة للحصول على عدد عروض الأسعار حسب الحالة
    public static function countByStatus($status)
    {
        return self::where('status', $status)->count();
    }

    // دالة مساعدة للحصول على نسبة التغيير في عدد عروض الأسعار
    public static function getChangePercentage($status, $days = 30)
    {
        $now = Carbon::now();
        $currentPeriodCount = self::where('status', $status)
            ->where('created_at', '>=', $now->copy()->subDays($days))
            ->count();

        $previousPeriodCount = self::where('status', $status)
            ->where('created_at', '<', $now->copy()->subDays($days))
            ->where('created_at', '>=', $now->copy()->subDays($days * 2))
            ->count();

        if ($previousPeriodCount == 0) {
            return $currentPeriodCount > 0 ? 100 : 0;
        }

        return round((($currentPeriodCount - $previousPeriodCount) / $previousPeriodCount) * 100);
    }

    /**
     * دالة محسنة لإنشاء رقم عرض السعر بصيغة QT-MKL25-01
     */
    public static function generateQuotationNumber($branch_code)
    {
        $year = date('y'); // آخر رقمين من السنة (مثل 25 لسنة 2025)

        return DB::transaction(function () use ($branch_code, $year) {
            // البحث عن آخر رقم عرض سعر للفرع في السنة الحالية
            $lastQuotation = self::where('quotation_number', 'like', 'QT-' . $branch_code . $year . '-%')
                            ->orderBy('quotation_number', 'desc')
                            ->lockForUpdate()
                            ->first();

            $nextSerial = 1;

            if ($lastQuotation) {
                // استخراج الرقم التسلسلي من آخر عرض سعر
                $pattern = '/QT-' . preg_quote($branch_code . $year . '-', '/') . '(\d+)$/';
                if (preg_match($pattern, $lastQuotation->quotation_number, $matches)) {
                    $nextSerial = intval($matches[1]) + 1;
                }
            }

            // التأكد من عدم وجود رقم مكرر
            do {
                $proposedNumber = 'QT-' . $branch_code . $year . '-' . str_pad($nextSerial, 2, '0', STR_PAD_LEFT);
                $exists = self::where('quotation_number', $proposedNumber)->exists();
                if ($exists) {
                    $nextSerial++;
                }
            } while ($exists);

            return $proposedNumber;
        });
    }

    /**
     * دالة للحصول على الرقم التسلسلي التالي للفرع في السنة الحالية
     */
    public static function getNextSerialForBranch($branch_code, $year = null)
    {
        if (!$year) {
            $year = date('y');
        }

        $lastQuotation = self::where('quotation_number', 'like', 'QT-' . $branch_code . $year . '-%')
                        ->orderBy('quotation_number', 'desc')
                        ->first();

        if ($lastQuotation) {
            $pattern = '/QT-' . preg_quote($branch_code . $year . '-', '/') . '(\d+)$/';
            if (preg_match($pattern, $lastQuotation->quotation_number, $matches)) {
                return intval($matches[1]) + 1;
            }
        }

        return 1; // البداية من 01
    }

    /**
     * دالة لعرض رقم عرض السعر بتنسيق جميل
     */
    public function getFormattedQuotationNumberAttribute()
    {
        return $this->quotation_number ?: 'غير محدد';
    }

    /**
     * دالة للحصول على حالة عرض السعر بالعربية
     */
    public function getStatusInArabicAttribute()
    {
        $statuses = [
            'draft' => 'مسودة',
            'sent' => 'مرسل',
            'accepted' => 'مقبول',
            'rejected' => 'مرفوض',
            'expired' => 'منتهي الصلاحية',
            'converted' => 'تم تحويله لطلب'
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * دالة للتحقق من انتهاء صلاحية عرض السعر
     */
    public function isExpired()
    {
        return $this->valid_until && Carbon::parse($this->valid_until)->isPast();
    }

    /**
     * دالة للحصول على الإحصائيات الموحدة لعروض الأسعار
     */
    public static function getUnifiedStatistics($user = null)
    {
        try {
            $user = $user ?: auth()->user();
            
            if (!$user) {
                return [
                    'total_quotations' => 0,
                    'draft_count' => 0,
                    'sent_count' => 0,
                    'accepted_count' => 0,
                    'rejected_count' => 0,
                    'expired_count' => 0,
                    'is_admin' => false,
                    'base_query' => null
                ];
            }

            // تحديد الاستعلام الأساسي حسب صلاحيات المستخدم
            if ($user->isAdmin()) {
                $baseQuery = self::query();
            } else {
                $baseQuery = self::where('user_id', $user->id);
            }

            // حساب الإحصائيات
            $totalQuotations = $baseQuery->count();
            $draftCount = (clone $baseQuery)->where('status', 'draft')->count();
            $sentCount = (clone $baseQuery)->where('status', 'sent')->count();
            $acceptedCount = (clone $baseQuery)->where('status', 'accepted')->count();
            $rejectedCount = (clone $baseQuery)->where('status', 'rejected')->count();
            $expiredCount = (clone $baseQuery)->where('status', 'expired')->count();

            return [
                'total_quotations' => $totalQuotations,
                'draft_count' => $draftCount,
                'sent_count' => $sentCount,
                'accepted_count' => $acceptedCount,
                'rejected_count' => $rejectedCount,
                'expired_count' => $expiredCount,
                'is_admin' => $user->isAdmin(),
                'base_query' => $baseQuery
            ];

        } catch (\Exception $e) {
            // في حالة حدوث خطأ، إرجاع قيم افتراضية
            \Log::error('خطأ في getUnifiedStatistics للعروض: ' . $e->getMessage());

            return [
                'total_quotations' => 0,
                'draft_count' => 0,
                'sent_count' => 0,
                'accepted_count' => 0,
                'rejected_count' => 0,
                'expired_count' => 0,
                'is_admin' => false,
                'base_query' => null
            ];
        }
    }
}
