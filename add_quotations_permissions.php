<?php
/**
 * إضافة صلاحيات عروض الأسعار إلى قاعدة البيانات
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'alahmadi_a';
$username = 'root';
$password = '';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>إضافة صلاحيات عروض الأسعار</h1>";
    
    // التحقق من وجود جدول permissions
    $checkTable = $pdo->query("SHOW TABLES LIKE 'permissions'")->rowCount();
    if ($checkTable == 0) {
        echo "<p style='color: red;'>❌ جدول permissions غير موجود</p>";
        exit;
    }
    
    echo "<h2>1. إضافة صلاحيات عروض الأسعار</h2>";
    
    // إدراج صلاحيات عروض الأسعار
    $permissions = [
        ['create_quotations', 'إنشاء عرض سعر', 'إنشاء عروض أسعار جديدة'],
        ['view_quotations', 'عرض عروض الأسعار', 'عرض قائمة عروض الأسعار'],
        ['edit_quotations', 'تعديل عروض الأسعار', 'تعديل عروض الأسعار الموجودة'],
        ['delete_quotations', 'حذف عروض الأسعار', 'حذف عروض الأسعار'],
        ['quotations_status', 'تغيير حالة عرض السعر', 'تغيير حالة عروض الأسعار'],
        ['convert_quotations', 'تحويل عرض السعر لطلب', 'تحويل عروض الأسعار إلى طلبات']
    ];
    
    $insertPermissionSQL = "
    INSERT INTO `permissions` (`name`, `display_name`, `description`, `module`, `created_at`, `updated_at`) VALUES
    (?, ?, ?, 'quotations', NOW(), NOW())
    ON DUPLICATE KEY UPDATE 
        `display_name` = VALUES(`display_name`),
        `description` = VALUES(`description`),
        `updated_at` = NOW()
    ";
    
    $stmt = $pdo->prepare($insertPermissionSQL);
    
    foreach ($permissions as $permission) {
        $stmt->execute($permission);
        echo "<p style='color: green;'>✓ تم إضافة صلاحية: {$permission[1]}</p>";
    }
    
    echo "<h2>2. ربط الصلاحيات بالأدوار</h2>";
    
    // الحصول على معرفات الأدوار
    $roles = $pdo->query("SELECT id, name, display_name FROM roles")->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($roles)) {
        echo "<p style='color: red;'>❌ لا توجد أدوار في قاعدة البيانات</p>";
    } else {
        // تحديد الصلاحيات لكل دور
        $rolePermissions = [
            'admin' => ['create_quotations', 'view_quotations', 'edit_quotations', 'delete_quotations', 'quotations_status', 'convert_quotations'],
            'manager' => ['create_quotations', 'view_quotations', 'edit_quotations', 'quotations_status', 'convert_quotations'],
            'employee' => ['create_quotations', 'view_quotations', 'edit_quotations'],
            'viewer' => ['view_quotations']
        ];
        
        $insertRolePermissionSQL = "
        INSERT INTO `role_permissions` (`role_id`, `permission_name`, `granted_at`, `granted_by`, `created_at`, `updated_at`)
        VALUES (?, ?, NOW(), 1, NOW(), NOW())
        ON DUPLICATE KEY UPDATE updated_at = NOW()
        ";
        
        $stmt = $pdo->prepare($insertRolePermissionSQL);
        
        foreach ($roles as $role) {
            if (isset($rolePermissions[$role['name']])) {
                echo "<h3>دور: {$role['display_name']}</h3>";
                foreach ($rolePermissions[$role['name']] as $permissionName) {
                    $stmt->execute([$role['id'], $permissionName]);
                    echo "<p style='color: green; margin-left: 20px;'>✓ تم ربط صلاحية: $permissionName</p>";
                }
            }
        }
    }
    
    echo "<h2>3. التحقق من النتائج</h2>";
    
    // عرض الصلاحيات المضافة
    echo "<h3>صلاحيات عروض الأسعار:</h3>";
    $quotationPermissions = $pdo->query("SELECT name, display_name, description FROM permissions WHERE module = 'quotations'")->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($quotationPermissions)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background-color: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>اسم الصلاحية</th>";
        echo "<th style='padding: 10px;'>الاسم المعروض</th>";
        echo "<th style='padding: 10px;'>الوصف</th>";
        echo "</tr>";
        
        foreach ($quotationPermissions as $permission) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($permission['name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($permission['display_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($permission['description']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // عرض ربط الصلاحيات بالأدوار
    echo "<h3>ربط الصلاحيات بالأدوار:</h3>";
    $rolePermissionsResult = $pdo->query("
        SELECT r.display_name as role_name, rp.permission_name 
        FROM role_permissions rp 
        JOIN roles r ON r.id = rp.role_id 
        WHERE rp.permission_name LIKE '%quotations%' OR rp.permission_name LIKE '%quotation%'
        ORDER BY r.display_name, rp.permission_name
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($rolePermissionsResult)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background-color: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>الدور</th>";
        echo "<th style='padding: 10px;'>الصلاحية</th>";
        echo "</tr>";
        
        foreach ($rolePermissionsResult as $rp) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($rp['role_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($rp['permission_name']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<div style='background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 تم إضافة صلاحيات عروض الأسعار بنجاح!</h3>";
    echo "<p>الآن يمكن للمستخدمين الوصول إلى وظائف عروض الأسعار حسب أدوارهم:</p>";
    echo "<ul>";
    echo "<li><strong>مدير النظام:</strong> جميع الصلاحيات</li>";
    echo "<li><strong>مدير القسم:</strong> إنشاء، عرض، تعديل، تغيير الحالة، تحويل لطلب</li>";
    echo "<li><strong>الموظف:</strong> إنشاء، عرض، تعديل</li>";
    echo "<li><strong>المشاهد:</strong> عرض فقط</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>الخطوات التالية:</h2>";
    echo "<ol>";
    echo "<li>تحديث صفحة الصلاحيات في المتصفح</li>";
    echo "<li>التحقق من ظهور صلاحيات عروض الأسعار</li>";
    echo "<li>اختبار الوصول إلى <a href='/quotations' target='_blank'>قائمة عروض الأسعار</a></li>";
    echo "<li>اختبار إنشاء <a href='/quotations/create' target='_blank'>عرض سعر جديد</a></li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<p style='color: red; font-size: 18px;'>✗ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red; font-size: 18px;'>✗ خطأ عام: " . $e->getMessage() . "</p>";
}
?>
