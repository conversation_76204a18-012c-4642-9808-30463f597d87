<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover, shrink-to-fit=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#2E4BC6">
    <meta name="msapplication-navbutton-color" content="#2E4BC6">
    <meta name="apple-mobile-web-app-title" content="تسجيل الدخول">
    <meta name="format-detection" content="telephone=no">
    <title>تسجيل الدخول - ASWSD</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts for Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&family=Tajawal:wght@300;400;500;700;800;900&display=swap" rel="stylesheet">
    <!-- Particles.js -->
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    <style>
        :root {
            --primary-blue: #2E4BC6;
            --secondary-blue: #1e3a8a;
            --accent-orange: #FF5722;
            --accent-gold: #fbbf24;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --bg-light: #f9fafb;
            --white: #ffffff;
            --border-light: #e5e7eb;
            --shadow-light: 0 4px 6px rgba(0, 0, 0, 0.05);
            --shadow-medium: 0 10px 25px rgba(0, 0, 0, 0.1);
            --shadow-heavy: 0 25px 50px rgba(0, 0, 0, 0.15);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            --gradient-secondary: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            --gradient-accent: linear-gradient(135deg, var(--primary-blue), var(--accent-orange));
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
            /* تحسينات للأجهزة المحمولة */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            /* منع الاهتزاز */
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
        }

        /* خلفية الجسيمات */
        #particles-js {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 1;
        }

        /* تأثير الموجات المتحركة */
        .wave-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(-45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            animation: wave 15s ease-in-out infinite;
            z-index: 2;
        }

        @keyframes wave {
            0%, 100% { transform: translateX(-100px) translateY(-100px) rotate(0deg); }
            50% { transform: translateX(100px) translateY(100px) rotate(180deg); }
        }

        .main-container {
            display: flex;
            gap: 40px;
            align-items: center;
            justify-content: center;
            width: 100%;
            max-width: 1200px;
            position: relative;
            z-index: 10;
        }

        .login-container {
            max-width: 450px;
            width: 100%;
            position: relative;
        }

        .services-container {
            max-width: 500px;
            width: 100%;
            position: relative;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            box-shadow: var(--shadow-heavy);
            padding: 50px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-secondary);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .login-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
        }

        .logo-container {
            margin-bottom: 35px;
            position: relative;
        }

        .logo {
            width: 100px;
            height: 100px;
            background: var(--gradient-accent);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            box-shadow: var(--shadow-medium);
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
            animation: logoFloat 6s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(2deg); }
        }

        .logo::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transform: rotate(45deg);
            animation: logoShine 3s linear infinite;
        }

        @keyframes logoShine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .logo:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 15px 35px rgba(46, 75, 198, 0.4);
        }

        .logo img {
            max-width: 70px;
            max-height: 70px;
            object-fit: contain;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
            transition: all 0.3s ease;
        }

        .logo:hover img {
            transform: scale(1.1);
        }

        .login-title {
            color: var(--text-dark);
            font-size: 2.2rem;
            font-weight: 800;
            margin-bottom: 15px;
            background: var(--gradient-accent);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: titleGlow 2s ease-in-out infinite alternate;
        }

        @keyframes titleGlow {
            from { filter: drop-shadow(0 0 5px rgba(46, 75, 198, 0.3)); }
            to { filter: drop-shadow(0 0 15px rgba(46, 75, 198, 0.6)); }
        }

        .login-subtitle {
            color: var(--text-light);
            font-size: 1rem;
            margin-bottom: 35px;
            font-weight: 500;
            opacity: 0;
            animation: slideInUp 0.8s ease 0.5s forwards;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-group {
            margin-bottom: 25px;
            text-align: right;
            position: relative;
        }

        .form-control {
            width: 100%;
            padding: 18px 25px;
            border: 2px solid var(--border-light);
            border-radius: 15px;
            font-size: 1.1rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.9);
            color: var(--text-dark);
            font-family: 'Cairo', sans-serif;
            backdrop-filter: blur(10px);
            position: relative;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: var(--white);
            box-shadow: 0 0 0 4px rgba(46, 75, 198, 0.15), 0 8px 25px rgba(46, 75, 198, 0.1);
            transform: translateY(-3px);
        }

        .form-control:hover {
            border-color: var(--primary-blue);
            transform: translateY(-1px);
        }

        .form-control.is-invalid {
            border-color: #ef4444;
            box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.15);
        }

        .form-label {
            display: block;
            margin-bottom: 12px;
            color: var(--text-dark);
            font-weight: 600;
            font-size: 1rem;
            position: relative;
            transition: all 0.3s ease;
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            cursor: pointer;
            padding: 10px;
            font-size: 1.3rem;
            z-index: 5;
            transition: all 0.3s ease;
            border-radius: 12px;
            min-width: 44px;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(46, 75, 198, 0.15);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            -webkit-tap-highlight-color: transparent;
        }

        .password-toggle:hover {
            color: var(--primary-blue);
            background: rgba(46, 75, 198, 0.08);
            transform: translateY(-50%) scale(1.05);
            border-color: var(--primary-blue);
            box-shadow: 0 5px 15px rgba(46, 75, 198, 0.25);
        }

        .password-toggle:active {
            transform: translateY(-50%) scale(0.95);
            background: rgba(46, 75, 198, 0.15);
            box-shadow: 0 2px 8px rgba(46, 75, 198, 0.3);
        }

        .remember-me {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 25px;
            font-size: 1rem;
            padding: 12px 0;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
            -webkit-tap-highlight-color: transparent;
        }

        .checkbox-container:hover {
            background: rgba(46, 75, 198, 0.05);
        }

        .checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-blue);
            cursor: pointer;
            border-radius: 4px;
            border: 2px solid var(--border-light);
            transition: all 0.3s ease;
        }

        .checkbox:checked {
            border-color: var(--primary-blue);
            background-color: var(--primary-blue);
            box-shadow: 0 2px 8px rgba(46, 75, 198, 0.3);
        }

        .checkbox:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(46, 75, 198, 0.15);
        }

        .checkbox-label {
            color: var(--text-dark);
            font-weight: 500;
            cursor: pointer;
            user-select: none;
            -webkit-user-select: none;
        }

        .forgot-password {
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 8px;
            border-radius: 6px;
            -webkit-tap-highlight-color: transparent;
        }

        .forgot-password:hover {
            color: var(--secondary-blue);
            background: rgba(46, 75, 198, 0.05);
            text-decoration: underline;
        }

        .btn-login {
            width: 100%;
            padding: 18px 25px;
            background: var(--gradient-accent);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            margin-bottom: 25px;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(46, 75, 198, 0.4);
            background: linear-gradient(135deg, var(--secondary-blue), var(--accent-orange));
        }

        .btn-login:active {
            transform: translateY(-1px);
            box-shadow: 0 8px 20px rgba(46, 75, 198, 0.3);
        }

        .btn-login:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .footer-text {
            color: var(--text-light);
            font-size: 0.8rem;
            margin-top: 20px;
        }

        .alert {
            background: linear-gradient(135deg, #fef2f2, #fee2e2);
            border: 2px solid #fca5a5;
            color: #dc2626;
            padding: 16px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-size: 1rem;
            text-align: right;
            position: relative;
            animation: alertSlide 0.5s ease-out;
            backdrop-filter: blur(10px);
        }

        @keyframes alertSlide {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .icon {
            color: var(--primary-blue);
            margin-left: 8px;
        }

        /* تأثيرات الموجة عند التركيز */
        .form-group::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 3px;
            background: var(--gradient-accent);
            transition: all 0.4s ease;
            transform: translateX(-50%);
            border-radius: 2px;
        }

        .form-group:focus-within::after {
            width: 100%;
        }

        /* تأثير الريبل عند النقر */
        .ripple {
            position: relative;
            overflow: hidden;
        }

        .ripple::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .ripple:active::after {
            width: 300px;
            height: 300px;
        }

        /* نموذج الخدمات - عرض البطاقات */
        .services-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            box-shadow: var(--shadow-heavy);
            padding: 30px;
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            animation: fadeInRight 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
            height: 500px;
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(50px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
        }

        .services-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(45deg, #4ade80, #06b6d4, #8b5cf6, #f59e0b);
            background-size: 400% 400%;
            animation: gradientShift 6s ease infinite;
        }

        .services-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .services-title {
            color: var(--text-dark);
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #4ade80, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .services-subtitle {
            color: var(--text-light);
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* حاوي البطاقات المتحركة */
        .services-carousel {
            position: relative;
            height: 320px;
            overflow: hidden;
            border-radius: 20px;
        }

        /* البطاقة الواحدة */
        .service-card {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
            border-radius: 20px;
            padding: 40px 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            opacity: 0;
            transform: translateX(100%) scale(0.8);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(15px);
        }

        .service-card.active {
            opacity: 1;
            transform: translateX(0) scale(1);
            z-index: 10;
        }

        .service-card.prev {
            opacity: 0;
            transform: translateX(-100%) scale(0.8);
        }

        .service-card.next {
            opacity: 0;
            transform: translateX(100%) scale(0.8);
        }

        /* أيقونة الخدمة الكبيرة */
        .service-card-icon {
            width: 100px;
            height: 100px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            margin-bottom: 25px;
            position: relative;
            overflow: hidden;
            animation: iconFloat 3s ease-in-out infinite;
        }

        @keyframes iconFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(5deg); }
        }

        .service-card-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transform: rotate(45deg);
            animation: iconShine 2s linear infinite;
        }

        @keyframes iconShine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .service-card-icon.orders { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }
        .service-card-icon.invoices { background: linear-gradient(135deg, #10b981, #047857); }
        .service-card-icon.branches { background: linear-gradient(135deg, #f59e0b, #d97706); }
        .service-card-icon.users { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
        .service-card-icon.reports { background: linear-gradient(135deg, #ef4444, #dc2626); }
        .service-card-icon.settings { background: linear-gradient(135deg, #6b7280, #4b5563); }

        /* عنوان الخدمة */
        .service-card-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 15px;
            animation: titlePulse 2s ease-in-out infinite;
        }

        @keyframes titlePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* وصف الخدمة */
        .service-card-description {
            font-size: 1.1rem;
            color: var(--text-light);
            line-height: 1.6;
            margin-bottom: 20px;
            opacity: 0;
            animation: fadeInUp 0.8s ease 0.5s forwards;
        }

        /* قائمة المميزات */
        .service-features {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .service-feature {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            opacity: 0;
            transform: translateY(20px);
            animation: slideInFeature 0.6s ease forwards;
        }

        .service-feature:nth-child(1) { animation-delay: 0.8s; }
        .service-feature:nth-child(2) { animation-delay: 1s; }
        .service-feature:nth-child(3) { animation-delay: 1.2s; }

        @keyframes slideInFeature {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .service-feature i {
            color: #10b981;
            margin-left: 8px;
            font-size: 0.9rem;
        }

        .service-feature span {
            color: var(--text-dark);
            font-size: 0.95rem;
            font-weight: 500;
        }

        /* مؤشرات البطاقات */
        .services-indicators {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 20px;
        }

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .indicator.active {
            background: var(--primary-blue);
            transform: scale(1.2);
        }

        .indicator:hover {
            background: var(--primary-blue);
            transform: scale(1.1);
        }

        /* تحسينات للشاشات الصغيرة */
        /* تأثيرات إضافية للبطاقات */
        .service-card:hover .service-card-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .service-card:hover .service-card-title {
            color: var(--primary-blue);
        }

        .service-card:hover .service-features {
            transform: translateY(-5px);
        }

        /* تأثير التمرير السلس */
        .services-carousel {
            scroll-behavior: smooth;
        }

        /* تحسينات متقدمة للشاشات الصغيرة */
        @media (max-width: 1024px) {
            .main-container {
                flex-direction: column;
                gap: 20px;
                padding: 15px 10px;
                min-height: 100vh;
                justify-content: flex-start;
                align-items: center;
            }

            .services-container {
                max-width: 95%;
                width: 100%;
                order: 1;
                /* إخفاء الخدمات على التابلت لتحسين الأداء */
                display: none;
            }

            .login-container {
                max-width: 95%;
                width: 100%;
                order: 1;
                margin-top: 5vh;
            }

            .services-card {
                height: 400px;
                margin-bottom: 20px;
            }

            .services-carousel {
                height: 250px;
            }

            /* تحسين الخلفية للتابلت */
            body {
                padding: 10px;
                align-items: center;
                justify-content: center;
                padding-top: 20px;
            }

            #particles-js {
                opacity: 0.5;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 20px 15px;
                gap: 0;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
            }

            .login-container {
                max-width: 100%;
                width: 100%;
                margin: 0;
            }

            .services-container {
                display: none; /* إخفاء الخدمات على الجوال */
            }

            .login-card {
                padding: 40px 25px;
                border-radius: 25px;
                margin: 0;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(20px);
            }

            .login-title {
                font-size: 2rem;
                margin-bottom: 15px;
                background: linear-gradient(135deg, var(--primary-blue), var(--accent-orange));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            .login-subtitle {
                font-size: 1rem;
                margin-bottom: 30px;
                color: var(--text-light);
            }

            .logo {
                width: 90px;
                height: 90px;
                margin-bottom: 25px;
                border-radius: 22px;
            }

            .logo img {
                max-width: 65px;
                max-height: 65px;
            }

            /* تحسين النماذج للجوال */
            .form-group {
                margin-bottom: 25px;
            }

            .form-control {
                padding: 18px 22px;
                font-size: 16px; /* منع التكبير على iOS */
                border-radius: 15px;
                border: 2px solid var(--border-light);
                background: rgba(255, 255, 255, 0.95);
                transition: all 0.3s ease;
            }

            .form-control:focus {
                border-color: var(--primary-blue);
                box-shadow: 0 0 0 4px rgba(46, 75, 198, 0.15);
                background: white;
            }

            .btn-login {
                padding: 18px 25px;
                font-size: 1.1rem;
                border-radius: 15px;
                margin-bottom: 25px;
                font-weight: 700;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .password-toggle {
                left: 18px;
                padding: 10px;
                min-width: 44px;
                min-height: 44px;
            }

            /* تحسين الخلفية للجوال */
            body {
                padding: 15px;
                background: var(--gradient-primary);
                background-attachment: fixed;
                background-size: 400% 400%;
                animation: gradientMove 15s ease infinite;
            }

            @keyframes gradientMove {
                0%, 100% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
            }

            #particles-js {
                opacity: 0.3;
            }

            .wave-bg {
                animation-duration: 25s;
                opacity: 0.6;
            }
        }

        @media (max-width: 480px) {
            .main-container {
                gap: 0;
                padding: 20px 15px;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
            }

            .login-container {
                width: 100%;
                max-width: 100%;
            }

            .login-card {
                padding: 35px 20px;
                border-radius: 20px;
                margin: 0;
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(15px);
                border: 1px solid rgba(255, 255, 255, 0.3);
            }

            .login-title {
                font-size: 1.8rem;
                margin-bottom: 12px;
                background: linear-gradient(135deg, var(--primary-blue), var(--accent-orange));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                font-weight: 800;
            }

            .login-subtitle {
                font-size: 0.9rem;
                margin-bottom: 25px;
                color: var(--text-light);
                line-height: 1.5;
            }

            .logo {
                width: 80px;
                height: 80px;
                margin-bottom: 20px;
                border-radius: 20px;
                background: var(--gradient-accent);
                box-shadow: 0 8px 20px rgba(46, 75, 198, 0.3);
            }

            .logo img {
                max-width: 55px;
                max-height: 55px;
            }

            .form-group {
                margin-bottom: 22px;
            }

            .form-control {
                padding: 16px 20px;
                font-size: 16px; /* منع التكبير على iOS */
                border-radius: 12px;
                border: 2px solid var(--border-light);
                background: rgba(255, 255, 255, 0.95);
                transition: all 0.3s ease;
                font-family: 'Cairo', sans-serif;
            }

            .form-control:focus {
                border-color: var(--primary-blue);
                box-shadow: 0 0 0 4px rgba(46, 75, 198, 0.15);
                background: white;
                outline: none;
            }

            .form-label {
                font-size: 0.95rem;
                margin-bottom: 10px;
                color: var(--text-dark);
                font-weight: 600;
            }

            .btn-login {
                padding: 16px 22px;
                font-size: 1rem;
                border-radius: 12px;
                margin-bottom: 20px;
                background: var(--gradient-accent);
                color: white;
                border: none;
                font-weight: 700;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                transition: all 0.3s ease;
                box-shadow: 0 8px 20px rgba(46, 75, 198, 0.3);
            }

            .btn-login:hover,
            .btn-login:active {
                background: linear-gradient(135deg, var(--secondary-blue), var(--accent-orange));
                box-shadow: 0 10px 25px rgba(46, 75, 198, 0.4);
            }

            .remember-me {
                font-size: 1rem;
                margin-bottom: 22px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 0;
            }

            .checkbox-container {
                display: flex;
                align-items: center;
                gap: 12px;
                cursor: pointer;
                padding: 10px;
                border-radius: 8px;
                transition: all 0.3s ease;
                -webkit-tap-highlight-color: transparent;
                min-height: 44px;
            }

            .checkbox-container:hover,
            .checkbox-container:active {
                background: rgba(46, 75, 198, 0.08);
            }

            .checkbox {
                width: 22px;
                height: 22px;
                accent-color: var(--primary-blue);
                cursor: pointer;
                border-radius: 4px;
                border: 2px solid var(--border-light);
                transition: all 0.3s ease;
            }

            .checkbox:checked {
                border-color: var(--primary-blue);
                background-color: var(--primary-blue);
                box-shadow: 0 2px 8px rgba(46, 75, 198, 0.3);
            }

            .checkbox-label {
                color: var(--text-dark);
                font-weight: 500;
                cursor: pointer;
                user-select: none;
                -webkit-user-select: none;
                font-size: 0.95rem;
            }

            .password-toggle {
                left: 12px;
                font-size: 1.2rem;
                padding: 12px;
                min-width: 48px;
                min-height: 48px;
                color: var(--text-light);
                transition: all 0.3s ease;
                border-radius: 10px;
                background: rgba(255, 255, 255, 0.98);
                border: 2px solid rgba(46, 75, 198, 0.2);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
            }

            .password-toggle:hover,
            .password-toggle:active {
                color: var(--primary-blue);
                background: rgba(46, 75, 198, 0.1);
                border-color: var(--primary-blue);
                transform: translateY(-50%) scale(1.02);
            }

            .alert {
                padding: 14px 18px;
                font-size: 0.95rem;
                border-radius: 12px;
                margin-bottom: 22px;
                background: linear-gradient(135deg, #fef2f2, #fee2e2);
                border: 2px solid #fca5a5;
                color: #dc2626;
            }

            .footer-text {
                font-size: 0.8rem;
                margin-top: 18px;
                color: var(--text-light);
                text-align: center;
            }

            /* تحسين الخلفية للهواتف الصغيرة */
            body {
                padding: 15px;
                min-height: 100vh;
                background: var(--gradient-primary);
                background-size: 400% 400%;
                animation: gradientMove 20s ease infinite;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

            @keyframes gradientMove {
                0%, 100% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
            }

            #particles-js {
                opacity: 0.2;
            }

            .wave-bg {
                opacity: 0.4;
                animation-duration: 30s;
            }

            /* تحسين الخدمات للهواتف الصغيرة */
            .services-card {
                padding: 20px 15px;
                height: auto;
                min-height: 400px;
                animation: none;
                transform: none;
            }

            .services-card::before {
                display: none;
            }

            .services-title {
                font-size: 1.3rem;
                margin-bottom: 8px;
            }

            .services-subtitle {
                font-size: 0.8rem;
            }

            .services-carousel {
                height: 250px;
            }

            .service-card {
                padding: 25px 20px;
                animation: none;
                transform: none;
            }

            .service-card-icon {
                width: 70px;
                height: 70px;
                font-size: 2rem;
                margin-bottom: 15px;
                border-radius: 18px;
                animation: none;
                transform: none;
            }

            .service-card-icon::before {
                display: none;
            }

            .service-card-title {
                font-size: 1.4rem;
                margin-bottom: 10px;
                animation: none;
                transform: none;
            }

            .service-card-description {
                font-size: 0.9rem;
                margin-bottom: 15px;
                animation: none;
            }

            .service-feature {
                margin-bottom: 8px;
                animation: none;
                transform: none;
            }

            .service-feature span {
                font-size: 0.85rem;
            }

            .indicator {
                width: 10px;
                height: 10px;
            }

            /* تحسين الأداء العام */
            * {
                -webkit-transform: translateZ(0);
                transform: translateZ(0);
                -webkit-backface-visibility: hidden;
                backface-visibility: hidden;
                -webkit-perspective: 1000;
                perspective: 1000;
            }

            /* إيقاف جميع التأثيرات المعقدة */
            .login-card::before,
            .services-card::before,
            .logo::before,
            .service-card-icon::before,
            .btn-login::before {
                display: none;
            }

            /* تحسين التمرير */
            body {
                -webkit-overflow-scrolling: touch;
                overflow-x: hidden;
            }
        }

        /* تأثيرات متقدمة */
        .fade-in {
            animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* تأثير الكتابة المتحركة */
        .typing-effect {
            overflow: hidden;
            border-left: 3px solid var(--primary-blue);
            white-space: nowrap;
            animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
        }

        /* إصلاح مشكلة الاهتزاز على الهاتف */

        /* تحسين التفاعل باللمس وإزالة الاهتزاز */
        @media (hover: none) and (pointer: coarse) {
            /* إيقاف جميع التأثيرات المتحركة المستمرة */
            .logo {
                animation: none;
                transform: none;
            }

            .login-title {
                animation: none;
                filter: none;
            }

            .wave-bg {
                animation: none;
                opacity: 0.2;
            }

            #particles-js {
                opacity: 0.1;
            }

            .login-card::before,
            .services-card::before {
                animation: none;
            }

            .logo::before,
            .service-card-icon::before {
                animation: none;
            }

            /* تحسين التفاعل باللمس */
            .form-control {
                -webkit-tap-highlight-color: transparent;
                touch-action: manipulation;
            }

            .form-control:focus {
                transform: none;
                box-shadow: 0 0 0 4px rgba(46, 75, 198, 0.15);
                border-color: var(--primary-blue);
            }

            .form-control:hover {
                transform: none;
            }

            .btn-login {
                -webkit-tap-highlight-color: transparent;
                touch-action: manipulation;
                cursor: pointer;
            }

            .btn-login:hover,
            .btn-login:active {
                transform: none;
                box-shadow: 0 8px 25px rgba(46, 75, 198, 0.3);
                background: linear-gradient(135deg, var(--secondary-blue), var(--accent-orange));
            }

            .logo:hover {
                transform: none;
                box-shadow: var(--shadow-medium);
            }

            .login-card:hover {
                transform: none;
                box-shadow: var(--shadow-heavy);
            }

            /* تحسين حجم منطقة اللمس */
            .password-toggle {
                padding: 12px;
                min-width: 44px;
                min-height: 44px;
                -webkit-tap-highlight-color: transparent;
                touch-action: manipulation;
            }

            .checkbox {
                width: 20px;
                height: 20px;
                min-width: 44px;
                min-height: 44px;
                -webkit-tap-highlight-color: transparent;
            }

            .indicator {
                width: 14px;
                height: 14px;
                min-width: 44px;
                min-height: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
                -webkit-tap-highlight-color: transparent;
                touch-action: manipulation;
            }

            /* إيقاف التأثيرات المتحركة للخدمات */
            .service-card-icon {
                animation: none;
                transform: none;
            }

            .service-card-title {
                animation: none;
                transform: none;
            }

            .service-card:hover .service-card-icon {
                transform: none;
            }

            .service-card:hover .service-card-title {
                color: var(--text-dark);
            }

            .service-card:hover .service-features {
                transform: none;
            }

            /* تحسين الأداء */
            * {
                -webkit-transform: translateZ(0);
                transform: translateZ(0);
                -webkit-backface-visibility: hidden;
                backface-visibility: hidden;
            }
        }

        /* تحسينات خاصة بالأجهزة المحمولة */

        /* تحسين التفاعل باللمس */
        @media (hover: none) and (pointer: coarse) {
            .form-control:focus {
                transform: none;
                box-shadow: 0 0 0 4px rgba(46, 75, 198, 0.15);
            }

            .btn-login:hover {
                transform: none;
                box-shadow: 0 8px 25px rgba(46, 75, 198, 0.3);
            }

            .logo:hover {
                transform: none;
            }

            .login-card:hover {
                transform: none;
            }

            /* تحسين حجم منطقة اللمس */
            .password-toggle {
                padding: 12px;
                min-width: 44px;
                min-height: 44px;
            }

            .checkbox {
                width: 20px;
                height: 20px;
            }

            .indicator {
                width: 14px;
                height: 14px;
                min-width: 44px;
                min-height: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        /* تحسين الأداء للأجهزة الضعيفة */
        @media (max-width: 480px) {
            /* تقليل التأثيرات المعقدة */
            .login-card::before,
            .services-card::before {
                animation-duration: 12s;
            }

            .logo::before,
            .service-card-icon::before {
                animation-duration: 4s;
            }

            .wave-bg {
                animation-duration: 30s;
            }

            /* تحسين الخطوط */
            * {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

            /* تحسين الانتقالات */
            .form-control,
            .btn-login {
                transition: all 0.2s ease;
            }
        }

        /* تحسين إمكانية الوصول */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* تحسين الألوان للوضع المظلم */
        @media (prefers-color-scheme: dark) {
            .login-card,
            .services-card {
                background: rgba(30, 30, 30, 0.95);
                border-color: rgba(255, 255, 255, 0.1);
            }

            .form-control {
                background: rgba(40, 40, 40, 0.9);
                color: #ffffff;
                border-color: rgba(255, 255, 255, 0.2);
            }

            .form-control:focus {
                background: rgba(50, 50, 50, 0.95);
            }

            .form-label {
                color: #e5e7eb;
            }

            .login-subtitle {
                color: #9ca3af;
            }
        }

        /* تحسين التمرير السلس */
        html {
            scroll-behavior: smooth;
        }

        /* تحسينات خاصة للهواتف الصغيرة جداً */
        @media (max-width: 360px) {
            .main-container {
                padding: 15px 10px;
            }

            .login-card {
                padding: 30px 18px;
                border-radius: 18px;
            }

            .login-title {
                font-size: 1.6rem;
                margin-bottom: 10px;
            }

            .login-subtitle {
                font-size: 0.85rem;
                margin-bottom: 20px;
            }

            .logo {
                width: 70px;
                height: 70px;
                margin-bottom: 18px;
                border-radius: 18px;
            }

            .logo img {
                max-width: 50px;
                max-height: 50px;
            }

            .form-control {
                padding: 14px 18px;
                font-size: 16px;
                border-radius: 10px;
            }

            .btn-login {
                padding: 14px 20px;
                font-size: 0.95rem;
                border-radius: 10px;
            }

            .form-group {
                margin-bottom: 20px;
            }

            .remember-me {
                font-size: 0.85rem;
                margin-bottom: 20px;
            }

            .password-toggle {
                left: 14px;
                min-width: 40px;
                min-height: 40px;
            }
        }

        /* تحسينات للأجهزة اللوحية الصغيرة */
        @media (min-width: 481px) and (max-width: 768px) {
            .main-container {
                padding: 30px 20px;
                justify-content: center;
            }

            .login-container {
                max-width: 450px;
            }

            .login-card {
                padding: 45px 35px;
                border-radius: 25px;
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            }

            .login-title {
                font-size: 2.2rem;
                margin-bottom: 15px;
            }

            .logo {
                width: 100px;
                height: 100px;
                margin-bottom: 25px;
            }

            .form-control {
                padding: 18px 25px;
                font-size: 1.1rem;
                border-radius: 15px;
            }

            .btn-login {
                padding: 18px 25px;
                font-size: 1.1rem;
                border-radius: 15px;
            }
        }

        /* منع التكبير غير المرغوب فيه على iOS */
        @supports (-webkit-touch-callout: none) {
            .form-control,
            input[type="email"],
            input[type="password"] {
                font-size: 16px !important;
            }

            /* تحسين التمرير على iOS */
            body {
                -webkit-overflow-scrolling: touch;
                -webkit-text-size-adjust: 100%;
            }

            .login-card {
                -webkit-transform: translateZ(0);
                transform: translateZ(0);
            }
        }

        @keyframes typing {
            from { width: 0; }
            to { width: 100%; }
        }

        @keyframes blink-caret {
            from, to { border-color: transparent; }
            50% { border-color: var(--primary-blue); }
        }

        /* تحسينات إضافية لمنع الاهتزاز */

        /* تحسين الأداء للأجهزة الضعيفة */
        @media (max-width: 768px) {
            /* تقليل التأثيرات المعقدة */
            .login-card::before,
            .services-card::before {
                animation-duration: 12s;
                will-change: auto;
            }

            .logo::before,
            .service-card-icon::before {
                animation-duration: 4s;
                will-change: auto;
            }

            .wave-bg {
                animation-duration: 30s;
                will-change: auto;
            }

            /* تحسين الخطوط */
            * {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                text-rendering: optimizeLegibility;
            }

            /* تحسين الانتقالات */
            .form-control,
            .btn-login {
                transition: all 0.2s ease;
                will-change: auto;
            }

            /* منع الاهتزاز عند التمرير */
            body {
                position: fixed;
                width: 100%;
                height: 100%;
                overflow: auto;
                -webkit-overflow-scrolling: touch;
            }

            .main-container {
                position: relative;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        /* تحسين إمكانية الوصول ومنع الاهتزاز */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
                scroll-behavior: auto !important;
            }

            .wave-bg,
            #particles-js {
                display: none !important;
            }
        }

        /* تحسين للأجهزة ذات الذاكرة المحدودة */
        @media (max-width: 480px) and (max-height: 800px) {
            /* إيقاف جميع التأثيرات المتحركة */
            * {
                animation: none !important;
                transition: none !important;
                transform: none !important;
            }

            .login-card,
            .services-card {
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                backdrop-filter: none;
                background: rgba(255, 255, 255, 0.98);
            }

            .form-control:focus {
                box-shadow: 0 0 0 2px rgba(46, 75, 198, 0.2);
            }

            .btn-login {
                background: var(--primary-blue);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            /* تحسين الذاكرة */
            .wave-bg,
            #particles-js,
            .login-card::before,
            .services-card::before,
            .logo::before,
            .service-card-icon::before,
            .btn-login::before {
                display: none !important;
            }
        }

        /* منع التكبير غير المرغوب فيه على iOS */
        @supports (-webkit-touch-callout: none) {
            .form-control,
            input[type="email"],
            input[type="password"] {
                font-size: 16px !important;
                transform: none !important;
            }

            /* منع الاهتزاز على Safari */
            body {
                -webkit-text-size-adjust: 100%;
                -webkit-tap-highlight-color: transparent;
            }

            .login-card,
            .services-card {
                -webkit-transform: translateZ(0);
                transform: translateZ(0);
            }
        }

        /* تحسين التمرير السلس ومنع الاهتزاز */
        html {
            scroll-behavior: smooth;
            -webkit-text-size-adjust: 100%;
        }

        body {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            -webkit-tap-highlight-color: transparent;
        }

        /* تحسينات خاصة للأداء على الهواتف */
        @media (max-width: 768px) {
            /* تحسين الذاكرة والأداء */
            .login-card::before,
            .services-card::before,
            .logo::before,
            .btn-login::before {
                content: none;
                display: none;
            }

            /* تحسين التمرير */
            body {
                -webkit-overflow-scrolling: touch;
                overflow-x: hidden;
                position: relative;
            }

            /* تحسين الخطوط */
            * {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                text-rendering: optimizeLegibility;
            }

            /* تحسين التفاعل */
            .form-control,
            .btn-login,
            .password-toggle {
                -webkit-tap-highlight-color: rgba(46, 75, 198, 0.2);
                tap-highlight-color: rgba(46, 75, 198, 0.2);
            }

            /* تحسين الانتقالات */
            .form-control,
            .btn-login {
                transition: all 0.2s ease;
            }
        }

        /* تحسين الأداء العام */
        .login-card,
        .services-card,
        .form-control,
        .btn-login {
            will-change: auto;
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
        }

        /* تحسين التركيز للوحة المفاتيح */
        .form-control:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 4px rgba(46, 75, 198, 0.15);
        }

        /* تحسين إمكانية الوصول */
        .btn-login:focus,
        .password-toggle:focus {
            outline: 2px solid var(--primary-blue);
            outline-offset: 2px;
        }

        /* تحسين الألوان للوضع المظلم */
        @media (prefers-color-scheme: dark) {
            .login-card {
                background: rgba(30, 30, 30, 0.95);
                border-color: rgba(255, 255, 255, 0.1);
                color: #ffffff;
            }

            .form-control {
                background: rgba(40, 40, 40, 0.9);
                color: #ffffff;
                border-color: rgba(255, 255, 255, 0.2);
            }

            .form-control:focus {
                background: rgba(50, 50, 50, 0.95);
                border-color: var(--primary-blue);
            }

            .form-label {
                color: #e5e7eb;
            }

            .login-subtitle {
                color: #9ca3af;
            }

            .password-toggle {
                color: #9ca3af;
            }

            .password-toggle:hover {
                color: var(--primary-blue);
            }
        }
    </style>
</head>
<body>
    <!-- خلفية الجسيمات -->
    <div id="particles-js"></div>

    <!-- تأثير الموجات -->
    <div class="wave-bg"></div>

    <div class="main-container">
        <!-- نموذج الخدمات -->
        <div class="services-container">
            <div class="services-card">
                <div class="services-header">
                    <h2 class="services-title">خدمات النظام</h2>
                    <p class="services-subtitle">نظام إدارة شامل لجميع احتياجاتك التجارية</p>
                </div>

                <!-- حاوي البطاقات المتحركة -->
                <div class="services-carousel">
                    <!-- بطاقة إدارة الطلبات -->
                    <div class="service-card active" data-service="orders">
                        <div class="service-card-icon orders">
                            <i class="fas fa-box"></i>
                        </div>
                        <h3 class="service-card-title">إدارة الطلبات</h3>
                        <p class="service-card-description">نظام متكامل لإدارة جميع الطلبات من الإنشاء حتى التسليم</p>
                        <ul class="service-features">
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>إنشاء طلبات جديدة بسهولة</span>
                            </li>
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>متابعة حالة الطلبات</span>
                            </li>
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>إدارة تفاصيل الشحن</span>
                            </li>
                        </ul>
                    </div>

                    <!-- بطاقة إدارة الفواتير -->
                    <div class="service-card" data-service="invoices">
                        <div class="service-card-icon invoices">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <h3 class="service-card-title">إدارة الفواتير</h3>
                        <p class="service-card-description">إنشاء وطباعة الفواتير بتصميم احترافي ومتابعة المدفوعات</p>
                        <ul class="service-features">
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>تصميم فواتير احترافية</span>
                            </li>
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>حساب الضرائب تلقائياً</span>
                            </li>
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>طباعة وتصدير PDF</span>
                            </li>
                        </ul>
                    </div>

                    <!-- بطاقة إدارة الفروع -->
                    <div class="service-card" data-service="branches">
                        <div class="service-card-icon branches">
                            <i class="fas fa-building"></i>
                        </div>
                        <h3 class="service-card-title">إدارة الفروع</h3>
                        <p class="service-card-description">إدارة شاملة لجميع فروع الشركة ومتابعة أداء كل فرع</p>
                        <ul class="service-features">
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>إضافة وإدارة الفروع</span>
                            </li>
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>متابعة أداء الفروع</span>
                            </li>
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>تقارير مفصلة لكل فرع</span>
                            </li>
                        </ul>
                    </div>

                    <!-- بطاقة إدارة المستخدمين -->
                    <div class="service-card" data-service="users">
                        <div class="service-card-icon users">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="service-card-title">إدارة المستخدمين</h3>
                        <p class="service-card-description">نظام صلاحيات متقدم لإدارة المستخدمين بأمان عالي</p>
                        <ul class="service-features">
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>إدارة الصلاحيات</span>
                            </li>
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>أمان متقدم</span>
                            </li>
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>تتبع نشاط المستخدمين</span>
                            </li>
                        </ul>
                    </div>

                    <!-- بطاقة التقارير -->
                    <div class="service-card" data-service="reports">
                        <div class="service-card-icon reports">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3 class="service-card-title">التقارير والإحصائيات</h3>
                        <p class="service-card-description">تقارير تفصيلية وإحصائيات دقيقة لاتخاذ قرارات مدروسة</p>
                        <ul class="service-features">
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>تقارير مرئية تفاعلية</span>
                            </li>
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>إحصائيات في الوقت الفعلي</span>
                            </li>
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>تصدير التقارير</span>
                            </li>
                        </ul>
                    </div>

                    <!-- بطاقة الإعدادات -->
                    <div class="service-card" data-service="settings">
                        <div class="service-card-icon settings">
                            <i class="fas fa-cog"></i>
                        </div>
                        <h3 class="service-card-title">إعدادات النظام</h3>
                        <p class="service-card-description">تخصيص كامل للنظام ليناسب احتياجات عملك</p>
                        <ul class="service-features">
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>تخصيص الواجهة</span>
                            </li>
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>إعدادات متقدمة</span>
                            </li>
                            <li class="service-feature">
                                <i class="fas fa-check"></i>
                                <span>نسخ احتياطية تلقائية</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- مؤشرات البطاقات -->
                <div class="services-indicators">
                    <div class="indicator active" data-index="0"></div>
                    <div class="indicator" data-index="1"></div>
                    <div class="indicator" data-index="2"></div>
                    <div class="indicator" data-index="3"></div>
                    <div class="indicator" data-index="4"></div>
                    <div class="indicator" data-index="5"></div>
                </div>
            </div>
        </div>

        <!-- نموذج تسجيل الدخول -->
        <div class="login-container">
            <div class="login-card fade-in">
                <div class="logo-container">
                    <div class="logo">
                        <img src="{{ asset('images/logos/aswsd.png') }}" alt="شعار الشركة">
                    </div>
                    <h1 class="login-title">تسجيل الدخول</h1>
                    <p class="login-subtitle typing-effect">مرحباً بك في نظام إدارة الطلبات المتطور</p>
                </div>

            @if ($errors->any())
                <div class="alert">
                    @foreach ($errors->all() as $error)
                        <div><i class="fas fa-exclamation-circle"></i> {{ $error }}</div>
                    @endforeach
                </div>
            @endif

            <form action="{{ route('login') }}" method="POST">
                @csrf
                
                <div class="form-group">
                    <label for="email" class="form-label">
                        <i class="fas fa-envelope icon"></i>البريد الإلكتروني
                    </label>
                    <input type="email" 
                           class="form-control @error('email') is-invalid @enderror" 
                           id="email" 
                           name="email" 
                           value="{{ old('email') }}" 
                           placeholder="أدخل بريدك الإلكتروني"
                           required 
                           autofocus>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock icon"></i>كلمة المرور
                    </label>
                    <div class="password-container">
                        <input type="password" 
                               class="form-control @error('password') is-invalid @enderror" 
                               id="password" 
                               name="password" 
                               placeholder="أدخل كلمة المرور"
                               required>
                        <span class="password-toggle" id="togglePassword">
                            <i class="fas fa-eye-slash"></i>
                        </span>
                    </div>
                </div>

                <div class="remember-me">
                    <div class="checkbox-container">
                        <input type="checkbox" class="checkbox" id="remember" name="remember">
                        <label for="remember" class="checkbox-label">تذكرني</label>
                    </div>
                    <a href="#" class="forgot-password" style="color: var(--primary-blue); text-decoration: none; font-size: 0.9rem; font-weight: 500;">نسيت كلمة المرور؟</a>
                </div>

                <button type="submit" class="btn-login ripple">
                    <i class="fas fa-sign-in-alt" style="margin-left: 8px;"></i>
                    تسجيل الدخول
                </button>
            </form>

            <div class="footer-text">
                جميع الحقوق محفوظة &copy; {{ date('Y') }} مجموعة ابراهيم الاحمدي اليمنية
            </div>
        </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // كشف الأجهزة المحمولة
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isSmallScreen = window.innerWidth <= 768;
            const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

            // تحسين الأداء للأجهزة المحمولة
            if (isMobile || isSmallScreen || prefersReducedMotion) {
                // إيقاف الجسيمات على الأجهزة المحمولة
                const particlesContainer = document.getElementById('particles-js');
                if (particlesContainer) {
                    particlesContainer.style.display = 'none';
                }

                // إيقاف الموجات المتحركة
                const waveBackground = document.querySelector('.wave-bg');
                if (waveBackground) {
                    waveBackground.style.display = 'none';
                }

                // إيقاف التأثيرات المتحركة المعقدة
                const animatedElements = document.querySelectorAll('.logo, .login-title, .service-card-icon, .service-card-title');
                animatedElements.forEach(element => {
                    element.style.animation = 'none';
                    element.style.transform = 'none';
                });

                // تحسين الأداء
                document.body.style.willChange = 'auto';
                document.body.style.transform = 'translateZ(0)';
            } else {
                // تهيئة الجسيمات للأجهزة المكتبية فقط
                particlesJS('particles-js', {
                    particles: {
                        number: { value: 60, density: { enable: true, value_area: 1000 } },
                        color: { value: '#ffffff' },
                        shape: { type: 'circle' },
                        opacity: { value: 0.3, random: false },
                        size: { value: 2, random: true },
                        line_linked: {
                            enable: true,
                            distance: 120,
                            color: '#ffffff',
                            opacity: 0.2,
                            width: 1
                        },
                        move: {
                            enable: true,
                            speed: 3,
                            direction: 'none',
                            random: false,
                            straight: false,
                            out_mode: 'out',
                            bounce: false
                        }
                    },
                    interactivity: {
                        detect_on: 'canvas',
                        events: {
                            onhover: { enable: false },
                            onclick: { enable: false },
                            resize: true
                        }
                    },
                    retina_detect: true
                });
            }

            // إظهار وإخفاء كلمة المرور
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');

            if (togglePassword && passwordInput) {
                togglePassword.addEventListener('click', function() {
                    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordInput.setAttribute('type', type);

                    const icon = this.querySelector('i');
                    icon.classList.toggle('fa-eye');
                    icon.classList.toggle('fa-eye-slash');

                    // تأثير بسيط للأجهزة المحمولة
                    if (!isMobile && !isSmallScreen) {
                        this.style.transform = 'scale(0.9)';
                        setTimeout(() => {
                            this.style.transform = 'scale(1)';
                        }, 150);
                    }
                });
            }

            // تأثير عند إرسال النموذج
            const form = document.querySelector('form');
            const loginButton = document.querySelector('.btn-login');

            if (form && loginButton) {
                form.addEventListener('submit', function(e) {
                    loginButton.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-left: 8px;"></i> جاري تسجيل الدخول...';
                    loginButton.disabled = true;

                    // إيقاف التأثيرات على الأجهزة المحمولة
                    if (!isMobile && !isSmallScreen) {
                        const loginCard = document.querySelector('.login-card');
                        if (loginCard) {
                            loginCard.style.animation = 'pulse 0.5s ease-in-out';
                        }
                    }
                });
            }

            // تركيز على حقل البريد الإلكتروني (بدون تأخير على الجوال)
            const emailInput = document.getElementById('email');
            if (emailInput && !emailInput.value) {
                if (isMobile || isSmallScreen) {
                    emailInput.focus();
                } else {
                    setTimeout(() => {
                        emailInput.focus();
                    }, 500);
                }
            }

            // تأثيرات محسنة للحقول
            const formControls = document.querySelectorAll('.form-control');
            formControls.forEach(control => {
                if (!isMobile && !isSmallScreen) {
                    control.addEventListener('focus', function() {
                        this.parentElement.style.transform = 'scale(1.02)';
                        this.parentElement.style.transition = 'transform 0.2s ease';
                    });

                    control.addEventListener('blur', function() {
                        this.parentElement.style.transform = 'scale(1)';
                    });
                } else {
                    // إزالة التحولات على الأجهزة المحمولة
                    control.addEventListener('focus', function() {
                        this.style.borderColor = '#2E4BC6';
                    });

                    control.addEventListener('blur', function() {
                        this.style.borderColor = '#e5e7eb';
                    });
                }
            });

            // تأثير الكتابة المتحركة للعنوان الفرعي (للأجهزة المكتبية فقط)
            const subtitle = document.querySelector('.login-subtitle');
            if (subtitle && !isMobile && !isSmallScreen && !prefersReducedMotion) {
                const text = subtitle.textContent;
                subtitle.textContent = '';
                subtitle.classList.remove('typing-effect');

                let i = 0;
                const typeWriter = () => {
                    if (i < text.length) {
                        subtitle.textContent += text.charAt(i);
                        i++;
                        setTimeout(typeWriter, 100);
                    }
                };

                setTimeout(typeWriter, 1000);
            }

            // تأثير النبضة للشعار (للأجهزة المكتبية فقط)
            const logo = document.querySelector('.logo');
            if (logo && !isMobile && !isSmallScreen && !prefersReducedMotion) {
                setInterval(() => {
                    logo.style.transform = 'scale(1.05)';
                    setTimeout(() => {
                        logo.style.transform = 'scale(1)';
                    }, 200);
                }, 5000);
            }

            // تأثيرات البطاقات المتحركة للخدمات (محسنة للجوال)
            const serviceCards = document.querySelectorAll('.service-card');
            const indicators = document.querySelectorAll('.indicator');
            let currentCardIndex = 0;
            let cardInterval;

            // دالة عرض البطاقة
            const showCard = (index) => {
                // إخفاء جميع البطاقات
                serviceCards.forEach((card, i) => {
                    card.classList.remove('active', 'prev', 'next');

                    if (i === index) {
                        card.classList.add('active');
                    } else if (i < index) {
                        card.classList.add('prev');
                    } else {
                        card.classList.add('next');
                    }
                });

                // تحديث المؤشرات
                indicators.forEach((indicator, i) => {
                    indicator.classList.toggle('active', i === index);
                });

                // تأثيرات إضافية للبطاقة النشطة (للأجهزة المكتبية فقط)
                if (!isMobile && !isSmallScreen && !prefersReducedMotion) {
                    const activeCard = serviceCards[index];
                    if (activeCard) {
                        // إعادة تشغيل الانيميشن للأيقونة
                        const icon = activeCard.querySelector('.service-card-icon');
                        if (icon) {
                            icon.style.animation = 'none';
                            setTimeout(() => {
                                icon.style.animation = 'iconFloat 3s ease-in-out infinite';
                            }, 100);
                        }

                        // إعادة تشغيل انيميشن المميزات
                        const features = activeCard.querySelectorAll('.service-feature');
                        features.forEach((feature, i) => {
                            feature.style.animation = 'none';
                            setTimeout(() => {
                                feature.style.animation = `slideInFeature 0.6s ease ${0.8 + i * 0.2}s forwards`;
                            }, 100);
                        });

                        // إعادة تشغيل انيميشن الوصف
                        const description = activeCard.querySelector('.service-card-description');
                        if (description) {
                            description.style.animation = 'none';
                            setTimeout(() => {
                                description.style.animation = 'fadeInUp 0.8s ease 0.5s forwards';
                            }, 100);
                        }
                    }
                }
            };

            // دالة الانتقال للبطاقة التالية
            const nextCard = () => {
                currentCardIndex = (currentCardIndex + 1) % serviceCards.length;
                showCard(currentCardIndex);
            };

            // بدء التشغيل التلقائي (مع تأخير أطول للجوال)
            const startAutoPlay = () => {
                const interval = isMobile || isSmallScreen ? 6000 : 4000; // تأخير أطول للجوال
                cardInterval = setInterval(nextCard, interval);
            };

            // إيقاف التشغيل التلقائي
            const stopAutoPlay = () => {
                if (cardInterval) {
                    clearInterval(cardInterval);
                }
            };

            // إضافة مستمعي الأحداث للمؤشرات
            indicators.forEach((indicator, index) => {
                indicator.addEventListener('click', () => {
                    currentCardIndex = index;
                    showCard(currentCardIndex);
                    stopAutoPlay();
                    const restartDelay = isMobile || isSmallScreen ? 10000 : 8000;
                    setTimeout(startAutoPlay, restartDelay);
                });

                // تحسين منطقة اللمس للجوال
                if (isMobile || isSmallScreen) {
                    indicator.style.minWidth = '44px';
                    indicator.style.minHeight = '44px';
                    indicator.style.display = 'flex';
                    indicator.style.alignItems = 'center';
                    indicator.style.justifyContent = 'center';
                }
            });

            // إضافة تأثيرات hover للبطاقات (للأجهزة المكتبية فقط)
            if (!isMobile && !isSmallScreen) {
                serviceCards.forEach(card => {
                    card.addEventListener('mouseenter', () => {
                        stopAutoPlay();
                        card.style.transform = 'translateX(0) scale(1.02)';
                    });

                    card.addEventListener('mouseleave', () => {
                        card.style.transform = '';
                        setTimeout(startAutoPlay, 2000);
                    });
                });
            }

            // بدء التشغيل (مع تأخير أقل للجوال)
            const startDelay = isMobile || isSmallScreen ? 500 : 1000;
            setTimeout(() => {
                showCard(0);
                if (!prefersReducedMotion) {
                    startAutoPlay();
                }
            }, startDelay);

            // تأثيرات إضافية للخدمات (للأجهزة المكتبية فقط)
            if (!isMobile && !isSmallScreen && !prefersReducedMotion) {
                const addServiceEffects = () => {
                    serviceCards.forEach((card, index) => {
                        const icon = card.querySelector('.service-card-icon');

                        // تأثير النبضة العشوائي
                        setTimeout(() => {
                            setInterval(() => {
                                if (card.classList.contains('active')) {
                                    icon.style.transform = 'scale(1.1)';
                                    setTimeout(() => {
                                        icon.style.transform = 'scale(1)';
                                    }, 200);
                                }
                            }, 6000 + Math.random() * 4000);
                        }, index * 1000);
                    });
                };

                setTimeout(addServiceEffects, 2000);
            }

            // تحسينات إضافية للأجهزة المحمولة
            if (isMobile || isSmallScreen) {
                // منع التمرير الأفقي غير المرغوب فيه
                document.body.style.overflowX = 'hidden';

                // تحسين الأداء
                document.body.style.willChange = 'auto';

                // إيقاف التأثيرات المعقدة
                const complexElements = document.querySelectorAll('.login-card, .services-card, .service-card-icon, .logo');
                complexElements.forEach(element => {
                    element.style.willChange = 'auto';
                    element.style.transform = 'translateZ(0)';
                });
            }
        });

        // تأثير الريبل المحسن (مبسط للجوال)
        document.addEventListener('click', function(e) {
            if (!isMobile && !isSmallScreen && !prefersReducedMotion) {
                if (e.target.classList.contains('ripple') || e.target.closest('.ripple')) {
                    const button = e.target.classList.contains('ripple') ? e.target : e.target.closest('.ripple');
                    const rect = button.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    const ripple = document.createElement('span');
                    ripple.style.cssText = `
                        position: absolute;
                        border-radius: 50%;
                        background: rgba(255, 255, 255, 0.6);
                        transform: scale(0);
                        animation: ripple-animation 0.6s linear;
                        left: ${x}px;
                        top: ${y}px;
                        width: ${size}px;
                        height: ${size}px;
                        pointer-events: none;
                    `;

                    button.appendChild(ripple);

                    setTimeout(() => {
                        if (ripple.parentNode) {
                            ripple.remove();
                        }
                    }, 600);
                }
            }
        });

        // إضافة CSS للتأثيرات المتقدمة (محسن للجوال)
        const style = document.createElement('style');

        if (isMobile || isSmallScreen || prefersReducedMotion) {
            // CSS مبسط للأجهزة المحمولة
            style.textContent = `
                .service-card-icon,
                .service-card-title,
                .service-card-description,
                .service-feature,
                .indicator {
                    transition: none !important;
                    animation: none !important;
                    transform: none !important;
                }
                .service-card.active {
                    opacity: 1;
                    transform: translateX(0) scale(1);
                }
                .service-card.prev {
                    opacity: 0;
                    transform: translateX(-100%) scale(0.9);
                }
                .service-card.next {
                    opacity: 0;
                    transform: translateX(100%) scale(0.9);
                }
                .services-carousel {
                    perspective: none;
                }
                .service-card {
                    transform-style: flat;
                    transition: opacity 0.3s ease, transform 0.3s ease;
                }
            `;
        } else {
            // CSS كامل للأجهزة المكتبية
            style.textContent = `
                @keyframes ripple-animation {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
                @keyframes pulse {
                    0%, 100% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                }
                @keyframes cardGlow {
                    0%, 100% {
                        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
                    }
                    50% {
                        box-shadow: 0 30px 60px rgba(59, 130, 246, 0.2);
                    }
                }
                .service-card.active {
                    animation: cardGlow 4s ease-in-out infinite;
                }
                .service-card-icon {
                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                }
                .service-card-title {
                    transition: all 0.3s ease;
                }
                .service-card-description {
                    transition: all 0.5s ease;
                }
                .service-feature {
                    transition: all 0.3s ease;
                }
                .indicator {
                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                }
                .services-carousel {
                    perspective: 1000px;
                }
                .service-card {
                    transform-style: preserve-3d;
                }
                .service-card.active {
                    transform: translateX(0) scale(1) rotateY(0deg);
                }
                .service-card.prev {
                    transform: translateX(-100%) scale(0.8) rotateY(-15deg);
                }
                .service-card.next {
                    transform: translateX(100%) scale(0.8) rotateY(15deg);
                }
            `;
        }

        document.head.appendChild(style);

        // تحسينات نهائية للأداء
        if (isMobile || isSmallScreen) {
            // تحسين الذاكرة
            window.addEventListener('beforeunload', function() {
                // تنظيف الفواصل الزمنية
                if (cardInterval) {
                    clearInterval(cardInterval);
                }

                // إزالة مستمعي الأحداث
                document.removeEventListener('click', arguments.callee);
            });

            // تحسين التمرير والتفاعل باللمس
            let touchStartY = 0;
            let touchEndY = 0;

            document.addEventListener('touchstart', function(e) {
                touchStartY = e.changedTouches[0].screenY;
            }, { passive: true });

            document.addEventListener('touchend', function(e) {
                touchEndY = e.changedTouches[0].screenY;
                handleSwipe();
            }, { passive: true });

            function handleSwipe() {
                const swipeThreshold = 50;
                const diff = touchStartY - touchEndY;

                // منع التمرير غير المرغوب فيه
                if (Math.abs(diff) < swipeThreshold) {
                    return;
                }

                // يمكن إضافة تفاعلات إضافية هنا
            }

            // تحسين الأداء للنماذج
            const formInputs = document.querySelectorAll('input, button');
            formInputs.forEach(input => {
                input.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                }, { passive: true });

                input.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                }, { passive: true });
            });

            // منع الاهتزاز على الهواتف
            document.addEventListener('touchmove', function(e) {
                if (e.target.closest('.main-container')) {
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const scrollHeight = document.documentElement.scrollHeight;
                    const clientHeight = document.documentElement.clientHeight;

                    // منع التمرير المطاطي
                    if (scrollTop === 0 && e.touches[0].clientY > touchStartY) {
                        e.preventDefault();
                    }
                    if (scrollTop >= scrollHeight - clientHeight && e.touches[0].clientY < touchStartY) {
                        e.preventDefault();
                    }
                }
            }, { passive: false });
        }
    </script>
</body>
</html>